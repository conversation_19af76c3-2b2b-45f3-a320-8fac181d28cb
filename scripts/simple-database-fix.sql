-- Simple Database Fix for EduBridge Signup Issues
-- This script fixes the "Database error saving new user" issue

-- 1. Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 2. Drop existing tables to start fresh (be careful in production!)
DROP TABLE IF EXISTS public.students CASCADE;
DROP TABLE IF EXISTS public.teachers CASCADE;
DROP TABLE IF EXISTS public.subjects CASCADE;
DROP TABLE IF EXISTS public.profiles CASCADE;

-- 3. Create profiles table that extends auth.users
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    role TEXT NOT NULL DEFAULT 'student' CHECK (role IN ('student', 'teacher', 'admin')),
    phone TEXT,
    date_of_birth DATE,
    city TEXT,
    province TEXT,
    gender TEXT CHECK (gender IN ('male', 'female', 'other')),
    address TEXT,
    emergency_contact TEXT,
    emergency_phone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create function to auto-create profiles for new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'role', 'student')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create trigger for auto profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 6. Create students table
CREATE TABLE public.students (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    class_level INTEGER NOT NULL CHECK (class_level >= 5 AND class_level <= 12),
    board TEXT NOT NULL CHECK (board IN ('punjab', 'sindh', 'kpk', 'federal', 'balochistan')),
    subjects TEXT[] DEFAULT '{}',
    parent_name TEXT,
    parent_phone TEXT,
    parent_email TEXT,
    learning_goals TEXT,
    preferred_subjects TEXT[] DEFAULT '{}',
    weak_subjects TEXT[] DEFAULT '{}',
    study_goals TEXT,
    preferred_study_time TEXT,
    budget_range TEXT,
    learning_style TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Create teachers table
CREATE TABLE public.teachers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    qualifications TEXT[] DEFAULT '{}',
    experience_years INTEGER DEFAULT 0,
    subjects TEXT[] DEFAULT '{}',
    class_levels INTEGER[] DEFAULT '{}',
    bio TEXT,
    hourly_rate DECIMAL(10,2) DEFAULT 0.00,
    is_verified BOOLEAN DEFAULT false,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_students INTEGER DEFAULT 0,
    total_hours_taught INTEGER DEFAULT 0,
    specialization TEXT,
    education_level TEXT,
    university TEXT,
    graduation_year TEXT,
    cnic TEXT,
    teaching_experience_description TEXT,
    teaching_style TEXT,
    languages TEXT[] DEFAULT '{}',
    availability_schedule JSONB DEFAULT '{}',
    min_hourly_rate DECIMAL(10,2) DEFAULT 0.00,
    max_hourly_rate DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Create subjects table
CREATE TABLE public.subjects (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    category TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. Insert default subjects
INSERT INTO public.subjects (name, category, description, is_active) VALUES
('Mathematics', 'mathematics', 'Mathematics for all levels', true),
('Additional Mathematics', 'mathematics', 'Advanced Mathematics', true),
('Physics', 'physics', 'Physics for all levels', true),
('Chemistry', 'chemistry', 'Chemistry for all levels', true),
('Biology', 'biology', 'Biology for all levels', true),
('English', 'language', 'English language and literature', true),
('Urdu', 'language', 'Urdu language and literature', true),
('Computer Science', 'technology', 'Computer Science and Programming', true),
('Economics', 'social_science', 'Economics for all levels', true),
('Accounting', 'commerce', 'Accounting and Finance', true),
('Business Studies', 'commerce', 'Business Studies', true),
('History', 'social_science', 'History and Social Studies', true),
('Geography', 'social_science', 'Geography', true),
('Islamic Studies', 'religious', 'Islamic Studies', true),
('Pakistan Studies', 'social_science', 'Pakistan Studies', true);

-- 10. Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.students ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subjects ENABLE ROW LEVEL SECURITY;

-- 11. Create RLS policies for profiles
CREATE POLICY "Enable read access for all users" ON public.profiles
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Enable update for users based on id" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- 12. Create RLS policies for students
CREATE POLICY "Students can view own data" ON public.students
    FOR SELECT USING (auth.uid() = profile_id);

CREATE POLICY "Students can insert own data" ON public.students
    FOR INSERT WITH CHECK (auth.uid() = profile_id);

CREATE POLICY "Students can update own data" ON public.students
    FOR UPDATE USING (auth.uid() = profile_id);

-- 13. Create RLS policies for teachers
CREATE POLICY "Teachers can view own data" ON public.teachers
    FOR SELECT USING (auth.uid() = profile_id);

CREATE POLICY "Teachers can insert own data" ON public.teachers
    FOR INSERT WITH CHECK (auth.uid() = profile_id);

CREATE POLICY "Teachers can update own data" ON public.teachers
    FOR UPDATE USING (auth.uid() = profile_id);

-- 14. Create RLS policies for subjects (public read)
CREATE POLICY "Everyone can view subjects" ON public.subjects
    FOR SELECT USING (true);

-- 15. Create updated_at trigger function
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 16. Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_students_updated_at 
    BEFORE UPDATE ON public.students
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_teachers_updated_at 
    BEFORE UPDATE ON public.teachers
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_subjects_updated_at 
    BEFORE UPDATE ON public.subjects
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- 17. Create indexes for performance
CREATE INDEX idx_profiles_email ON public.profiles(email);
CREATE INDEX idx_profiles_role ON public.profiles(role);
CREATE INDEX idx_students_profile_id ON public.students(profile_id);
CREATE INDEX idx_teachers_profile_id ON public.teachers(profile_id);
CREATE INDEX idx_subjects_name ON public.subjects(name);
CREATE INDEX idx_subjects_category ON public.subjects(category);

-- 18. Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- 19. Success message
SELECT 
    'Database setup completed successfully!' as message,
    'Auto-profile creation trigger installed' as trigger_status,
    (SELECT count(*) FROM public.subjects) as subjects_count,
    current_timestamp as completed_at;
