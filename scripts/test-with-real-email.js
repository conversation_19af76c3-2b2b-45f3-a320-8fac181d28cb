#!/usr/bin/env node

/**
 * Test signup with a real email address
 * This script helps you test the complete signup flow with your own email
 */

const { createClient } = require('@supabase/supabase-js')
const readline = require('readline')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer)
    })
  })
}

async function testSignupWithRealEmail() {
  console.log('🚀 EduBridge Signup Test with Real Email\n')
  
  console.log('This will test the complete signup flow with your email address.')
  console.log('You will receive a real confirmation email.\n')
  
  const email = await askQuestion('📧 Enter your email address: ')
  
  if (!email || !email.includes('@')) {
    console.log('❌ Invalid email address')
    rl.close()
    return
  }
  
  const fullName = await askQuestion('👤 Enter your full name: ')
  const role = await askQuestion('🎭 Enter your role (student/teacher): ')
  
  if (!['student', 'teacher'].includes(role.toLowerCase())) {
    console.log('❌ Role must be either "student" or "teacher"')
    rl.close()
    return
  }
  
  const password = await askQuestion('🔒 Enter a password (min 8 characters): ')
  
  if (password.length < 8) {
    console.log('❌ Password must be at least 8 characters')
    rl.close()
    return
  }
  
  console.log('\n🔄 Creating account...')
  
  try {
    const { data, error } = await supabase.auth.signUp({
      email: email.trim(),
      password: password,
      options: {
        data: {
          full_name: fullName.trim(),
          role: role.toLowerCase(),
          email: email.trim()
        },
        emailRedirectTo: 'http://localhost:3000/auth/confirm'
      }
    })
    
    if (error) {
      console.error('❌ Signup failed:', error.message)
      rl.close()
      return
    }
    
    if (data.user) {
      console.log('✅ Account created successfully!')
      console.log(`📧 User ID: ${data.user.id}`)
      console.log(`📧 Email: ${data.user.email}`)
      console.log(`📧 Email confirmed: ${data.user.email_confirmed_at ? 'Yes' : 'No'}`)
      
      // Wait for auto-profile creation
      console.log('\n⏳ Checking if profile was auto-created...')
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .maybeSingle()
      
      if (profileError) {
        console.error('❌ Profile check failed:', profileError.message)
      } else if (profile) {
        console.log('✅ Profile auto-created successfully!')
        console.log(`   - Name: ${profile.full_name}`)
        console.log(`   - Role: ${profile.role}`)
        console.log(`   - Email: ${profile.email}`)
      } else {
        console.log('⚠️  No profile found')
      }
      
      console.log('\n📬 IMPORTANT: Check your email for the confirmation link!')
      console.log('📋 Next steps:')
      console.log('1. Open your email inbox')
      console.log('2. Look for an email from Supabase/EduBridge')
      console.log('3. Click the confirmation link in the email')
      console.log('4. Complete your profile setup')
      console.log('5. Start using EduBridge!')
      
      console.log('\n🌐 You can also test the signup flow in your browser:')
      console.log('   http://localhost:3000/auth/signup')
      
    } else {
      console.log('❌ No user data returned')
    }
    
  } catch (error) {
    console.error('❌ Signup failed:', error.message)
  }
  
  rl.close()
}

async function main() {
  try {
    await testSignupWithRealEmail()
  } catch (error) {
    console.error('💥 Test failed:', error)
    rl.close()
  }
}

main()
