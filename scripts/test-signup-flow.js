#!/usr/bin/env node

/**
 * Test Signup Flow
 * 
 * This script tests the signup flow to ensure everything is working correctly.
 * It checks database connectivity and profile creation.
 */

const { createClient } = require('@supabase/supabase-js')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...')
  
  try {
    // Test basic connection
    const { data, error } = await supabase.from('profiles').select('count').limit(1)
    
    if (error) {
      console.error('❌ Database connection failed:', error.message)
      return false
    }
    
    console.log('✅ Database connection successful')
    return true
  } catch (error) {
    console.error('❌ Database connection error:', error.message)
    return false
  }
}

async function testRequiredTables() {
  console.log('🔍 Testing required tables...')
  
  const tables = ['profiles', 'students', 'teachers', 'subjects']
  const results = {}
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase.from(table).select('count').limit(1)
      
      if (error) {
        console.log(`❌ Table '${table}' not accessible: ${error.message}`)
        results[table] = false
      } else {
        console.log(`✅ Table '${table}' accessible`)
        results[table] = true
      }
    } catch (error) {
      console.log(`❌ Table '${table}' error: ${error.message}`)
      results[table] = false
    }
  }
  
  return results
}

async function testProfileCreation() {
  console.log('🔍 Testing profile creation permissions...')
  
  try {
    // Test if we can insert a test profile (this will fail due to RLS, but we can check the error)
    const { error } = await supabase
      .from('profiles')
      .insert({
        id: '00000000-0000-0000-0000-000000000000',
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'student'
      })
    
    if (error && error.code === '42501') {
      console.log('✅ Profile table has proper RLS (Row Level Security) enabled')
      return true
    } else if (error && error.code === '23505') {
      console.log('✅ Profile table accessible (duplicate key error expected)')
      return true
    } else if (error) {
      console.log(`⚠️  Profile table error: ${error.message}`)
      return false
    } else {
      console.log('⚠️  Profile table allows anonymous inserts (check RLS)')
      return true
    }
  } catch (error) {
    console.log(`❌ Profile creation test error: ${error.message}`)
    return false
  }
}

async function runTests() {
  console.log('🚀 Starting EduBridge Signup Flow Tests\n')
  
  const dbConnection = await testDatabaseConnection()
  console.log('')
  
  const tableResults = await testRequiredTables()
  console.log('')
  
  const profileTest = await testProfileCreation()
  console.log('')
  
  // Summary
  console.log('📊 Test Summary:')
  console.log('================')
  console.log(`Database Connection: ${dbConnection ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Profiles Table: ${tableResults.profiles ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Students Table: ${tableResults.students ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Teachers Table: ${tableResults.teachers ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Subjects Table: ${tableResults.subjects ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Profile Creation: ${profileTest ? '✅ PASS' : '❌ FAIL'}`)
  
  const allPassed = dbConnection && 
                   tableResults.profiles && 
                   tableResults.students && 
                   tableResults.teachers && 
                   profileTest
  
  console.log('\n' + (allPassed ? '🎉 All tests passed! Signup flow should work correctly.' : '⚠️  Some tests failed. Check the issues above.'))
  
  if (!allPassed) {
    console.log('\n📋 Next Steps:')
    if (!dbConnection) console.log('- Check your Supabase URL and API keys')
    if (!tableResults.profiles) console.log('- Run the database schema setup')
    if (!tableResults.students || !tableResults.teachers) console.log('- Ensure all required tables are created')
    if (!tableResults.subjects) console.log('- Run the subjects data setup')
    if (!profileTest) console.log('- Check RLS policies on profiles table')
  }
}

// Run the tests
runTests().catch(console.error)
