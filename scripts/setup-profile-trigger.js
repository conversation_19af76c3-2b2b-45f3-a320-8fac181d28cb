#!/usr/bin/env node

/**
 * Setup Profile Auto-Creation Trigger
 * 
 * This script sets up a database trigger to automatically create profiles
 * when users sign up through Supabase Auth.
 * 
 * Run this script after setting up your Supabase project.
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupProfileTrigger() {
  try {
    console.log('🚀 Setting up profile auto-creation trigger...')

    // Read the SQL file
    const sqlPath = path.join(__dirname, '..', 'database', 'auto-profile-trigger.sql')
    const sql = fs.readFileSync(sqlPath, 'utf8')

    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql })

    if (error) {
      console.error('❌ Error setting up trigger:', error)
      process.exit(1)
    }

    console.log('✅ Profile auto-creation trigger set up successfully!')
    console.log('📝 Users will now automatically get profiles when they sign up')

  } catch (error) {
    console.error('❌ Error:', error.message)
    process.exit(1)
  }
}

// Alternative method using direct SQL execution
async function setupProfileTriggerDirect() {
  try {
    console.log('🚀 Setting up profile auto-creation trigger...')

    const sql = `
-- Function to create profile automatically
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'role', 'student')::user_role
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger on auth.users table
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.profiles TO anon, authenticated;
    `

    const { error } = await supabase.from('_sql').select('*').limit(0)
    
    if (error) {
      console.log('⚠️  Direct SQL execution not available, please run the SQL manually in Supabase dashboard')
      console.log('📋 Copy and paste this SQL in your Supabase SQL editor:')
      console.log('\n' + sql + '\n')
    } else {
      console.log('✅ Profile auto-creation trigger set up successfully!')
    }

  } catch (error) {
    console.error('❌ Error:', error.message)
    console.log('📋 Please run this SQL manually in your Supabase SQL editor:')
    
    const sqlPath = path.join(__dirname, '..', 'database', 'auto-profile-trigger.sql')
    const sql = fs.readFileSync(sqlPath, 'utf8')
    console.log('\n' + sql + '\n')
  }
}

// Run the setup
setupProfileTriggerDirect()
