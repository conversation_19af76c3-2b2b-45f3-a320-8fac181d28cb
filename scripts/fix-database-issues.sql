-- EduBridge Database Fix Script
-- Run this in your Supabase SQL editor to fix all database issues

-- 1. Ensure all required extensions are enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 2. Create custom types if they don't exist
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('student', 'teacher', 'admin');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE board_type AS ENUM ('punjab', 'sindh', 'kpk', 'federal', 'balochistan');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE province_type AS ENUM ('punjab', 'sindh', 'kpk', 'balochistan', 'islamabad');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 3. Create profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    role user_role NOT NULL DEFAULT 'student',
    phone TEXT,
    date_of_birth DATE,
    city TEXT,
    province province_type,
    gender TEXT CHECK (gender IN ('male', 'female', 'other')),
    address TEXT,
    emergency_contact TEXT,
    emergency_phone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Add missing columns to profiles table if they don't exist
DO $$ 
BEGIN
    -- Add gender column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'gender') THEN
        ALTER TABLE profiles ADD COLUMN gender TEXT CHECK (gender IN ('male', 'female', 'other'));
    END IF;
    
    -- Add address column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'address') THEN
        ALTER TABLE profiles ADD COLUMN address TEXT;
    END IF;
    
    -- Add emergency_contact column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'emergency_contact') THEN
        ALTER TABLE profiles ADD COLUMN emergency_contact TEXT;
    END IF;
    
    -- Add emergency_phone column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'emergency_phone') THEN
        ALTER TABLE profiles ADD COLUMN emergency_phone TEXT;
    END IF;
END $$;

-- 5. Create students table if it doesn't exist
CREATE TABLE IF NOT EXISTS students (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    class_level INTEGER NOT NULL CHECK (class_level >= 5 AND class_level <= 12),
    board board_type NOT NULL,
    subjects TEXT[] DEFAULT '{}',
    parent_name TEXT,
    parent_phone TEXT,
    parent_email TEXT,
    learning_goals TEXT,
    preferred_subjects TEXT[] DEFAULT '{}',
    weak_subjects TEXT[] DEFAULT '{}',
    study_goals TEXT,
    preferred_study_time TEXT CHECK (preferred_study_time IN ('morning', 'afternoon', 'evening', 'night', 'flexible')),
    budget_range TEXT CHECK (budget_range IN ('under_1000', '1000_3000', '3000_5000', '5000_10000', 'above_10000')),
    learning_style TEXT CHECK (learning_style IN ('visual', 'auditory', 'kinesthetic', 'reading_writing', 'mixed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Add missing columns to students table if they don't exist
DO $$ 
BEGIN
    -- Add preferred_subjects column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'students' AND column_name = 'preferred_subjects') THEN
        ALTER TABLE students ADD COLUMN preferred_subjects TEXT[] DEFAULT '{}';
    END IF;
    
    -- Add weak_subjects column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'students' AND column_name = 'weak_subjects') THEN
        ALTER TABLE students ADD COLUMN weak_subjects TEXT[] DEFAULT '{}';
    END IF;
    
    -- Add study_goals column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'students' AND column_name = 'study_goals') THEN
        ALTER TABLE students ADD COLUMN study_goals TEXT;
    END IF;
    
    -- Add preferred_study_time column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'students' AND column_name = 'preferred_study_time') THEN
        ALTER TABLE students ADD COLUMN preferred_study_time TEXT CHECK (preferred_study_time IN ('morning', 'afternoon', 'evening', 'night', 'flexible'));
    END IF;
    
    -- Add budget_range column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'students' AND column_name = 'budget_range') THEN
        ALTER TABLE students ADD COLUMN budget_range TEXT CHECK (budget_range IN ('under_1000', '1000_3000', '3000_5000', '5000_10000', 'above_10000'));
    END IF;
    
    -- Add learning_style column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'students' AND column_name = 'learning_style') THEN
        ALTER TABLE students ADD COLUMN learning_style TEXT CHECK (learning_style IN ('visual', 'auditory', 'kinesthetic', 'reading_writing', 'mixed'));
    END IF;
END $$;

-- 7. Create teachers table if it doesn't exist
CREATE TABLE IF NOT EXISTS teachers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    qualifications TEXT[] DEFAULT '{}',
    experience_years INTEGER DEFAULT 0,
    subjects TEXT[] DEFAULT '{}',
    class_levels INTEGER[] DEFAULT '{}',
    bio TEXT,
    hourly_rate DECIMAL(10,2) DEFAULT 0.00,
    is_verified BOOLEAN DEFAULT false,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_students INTEGER DEFAULT 0,
    total_hours_taught INTEGER DEFAULT 0,
    specialization TEXT,
    education_level TEXT,
    university TEXT,
    graduation_year TEXT,
    cnic TEXT,
    teaching_experience_description TEXT,
    teaching_style TEXT,
    languages TEXT[] DEFAULT '{}',
    availability_schedule JSONB DEFAULT '{}',
    min_hourly_rate DECIMAL(10,2) DEFAULT 0.00,
    max_hourly_rate DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Add missing columns to teachers table if they don't exist
DO $$ 
BEGIN
    -- Add specialization column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teachers' AND column_name = 'specialization') THEN
        ALTER TABLE teachers ADD COLUMN specialization TEXT;
    END IF;
    
    -- Add education_level column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teachers' AND column_name = 'education_level') THEN
        ALTER TABLE teachers ADD COLUMN education_level TEXT;
    END IF;
    
    -- Add university column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teachers' AND column_name = 'university') THEN
        ALTER TABLE teachers ADD COLUMN university TEXT;
    END IF;
    
    -- Add graduation_year column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teachers' AND column_name = 'graduation_year') THEN
        ALTER TABLE teachers ADD COLUMN graduation_year TEXT;
    END IF;
    
    -- Add cnic column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teachers' AND column_name = 'cnic') THEN
        ALTER TABLE teachers ADD COLUMN cnic TEXT;
    END IF;
    
    -- Add teaching_experience_description column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teachers' AND column_name = 'teaching_experience_description') THEN
        ALTER TABLE teachers ADD COLUMN teaching_experience_description TEXT;
    END IF;
    
    -- Add teaching_style column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teachers' AND column_name = 'teaching_style') THEN
        ALTER TABLE teachers ADD COLUMN teaching_style TEXT;
    END IF;
    
    -- Add languages column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teachers' AND column_name = 'languages') THEN
        ALTER TABLE teachers ADD COLUMN languages TEXT[] DEFAULT '{}';
    END IF;
    
    -- Add availability_schedule column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teachers' AND column_name = 'availability_schedule') THEN
        ALTER TABLE teachers ADD COLUMN availability_schedule JSONB DEFAULT '{}';
    END IF;
    
    -- Add min_hourly_rate column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teachers' AND column_name = 'min_hourly_rate') THEN
        ALTER TABLE teachers ADD COLUMN min_hourly_rate DECIMAL(10,2) DEFAULT 0.00;
    END IF;
    
    -- Add max_hourly_rate column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teachers' AND column_name = 'max_hourly_rate') THEN
        ALTER TABLE teachers ADD COLUMN max_hourly_rate DECIMAL(10,2) DEFAULT 0.00;
    END IF;
END $$;

-- 9. Create subjects table if it doesn't exist
CREATE TABLE IF NOT EXISTS subjects (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. Insert default subjects if table is empty
INSERT INTO subjects (name, category, description, is_active) 
SELECT * FROM (VALUES
    ('Mathematics', 'Science', 'Mathematics for all levels', true),
    ('Physics', 'Science', 'Physics for all levels', true),
    ('Chemistry', 'Science', 'Chemistry for all levels', true),
    ('Biology', 'Science', 'Biology for all levels', true),
    ('English', 'Language', 'English language and literature', true),
    ('Urdu', 'Language', 'Urdu language and literature', true),
    ('Computer Science', 'Technology', 'Computer Science and Programming', true),
    ('Economics', 'Social Science', 'Economics for all levels', true),
    ('Accounting', 'Commerce', 'Accounting and Finance', true),
    ('Business Studies', 'Commerce', 'Business Studies', true),
    ('History', 'Social Science', 'History and Social Studies', true),
    ('Geography', 'Social Science', 'Geography', true),
    ('Islamic Studies', 'Religious', 'Islamic Studies', true),
    ('Pakistan Studies', 'Social Science', 'Pakistan Studies', true)
) AS v(name, category, description, is_active)
WHERE NOT EXISTS (SELECT 1 FROM subjects WHERE subjects.name = v.name);

-- 11. Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 12. Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_students_updated_at ON students;
CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON students
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_teachers_updated_at ON teachers;
CREATE TRIGGER update_teachers_updated_at BEFORE UPDATE ON teachers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_subjects_updated_at ON subjects;
CREATE TRIGGER update_subjects_updated_at BEFORE UPDATE ON subjects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 13. Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;

-- 14. Create RLS policies
-- Profiles: Users can read all profiles, but only update their own
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON profiles;
CREATE POLICY "Public profiles are viewable by everyone" ON profiles
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Students: Users can only access their own student data
DROP POLICY IF EXISTS "Students can view own data" ON students;
CREATE POLICY "Students can view own data" ON students
    FOR SELECT USING (auth.uid() = profile_id);

DROP POLICY IF EXISTS "Students can update own data" ON students;
CREATE POLICY "Students can update own data" ON students
    FOR UPDATE USING (auth.uid() = profile_id);

DROP POLICY IF EXISTS "Students can insert own data" ON students;
CREATE POLICY "Students can insert own data" ON students
    FOR INSERT WITH CHECK (auth.uid() = profile_id);

-- Teachers: Users can only access their own teacher data
DROP POLICY IF EXISTS "Teachers can view own data" ON teachers;
CREATE POLICY "Teachers can view own data" ON teachers
    FOR SELECT USING (auth.uid() = profile_id);

DROP POLICY IF EXISTS "Teachers can update own data" ON teachers;
CREATE POLICY "Teachers can update own data" ON teachers
    FOR UPDATE USING (auth.uid() = profile_id);

DROP POLICY IF EXISTS "Teachers can insert own data" ON teachers;
CREATE POLICY "Teachers can insert own data" ON teachers
    FOR INSERT WITH CHECK (auth.uid() = profile_id);

-- Subjects: Everyone can read subjects
DROP POLICY IF EXISTS "Everyone can view subjects" ON subjects;
CREATE POLICY "Everyone can view subjects" ON subjects
    FOR SELECT USING (true);

-- 15. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_students_profile_id ON students(profile_id);
CREATE INDEX IF NOT EXISTS idx_students_class_level ON students(class_level);
CREATE INDEX IF NOT EXISTS idx_students_board ON students(board);
CREATE INDEX IF NOT EXISTS idx_teachers_profile_id ON teachers(profile_id);
CREATE INDEX IF NOT EXISTS idx_teachers_subjects ON teachers USING GIN(subjects);
CREATE INDEX IF NOT EXISTS idx_teachers_class_levels ON teachers USING GIN(class_levels);
CREATE INDEX IF NOT EXISTS idx_teachers_is_verified ON teachers(is_verified);
CREATE INDEX IF NOT EXISTS idx_subjects_name ON subjects(name);
CREATE INDEX IF NOT EXISTS idx_subjects_category ON subjects(category);
CREATE INDEX IF NOT EXISTS idx_subjects_is_active ON subjects(is_active);

-- Success message
SELECT 'Database setup completed successfully!' as message;
