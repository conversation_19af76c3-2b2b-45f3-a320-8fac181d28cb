-- EduBridge Database Verification Script
-- Run this in your Supabase SQL editor to verify and fix database setup

-- 1. Check if required extensions are enabled
SELECT 
    extname as extension_name,
    extversion as version
FROM pg_extension 
WHERE extname IN ('uuid-ossp');

-- 2. Check if custom types exist
SELECT 
    typname as type_name,
    typtype as type_type
FROM pg_type 
WHERE typname IN ('user_role', 'board_type', 'province_type');

-- 3. Check if tables exist and their structure
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('profiles', 'students', 'teachers', 'subjects')
ORDER BY table_name, ordinal_position;

-- 4. Check RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'students', 'teachers', 'subjects');

-- 5. Check if RLS is enabled on tables
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'students', 'teachers', 'subjects');

-- 6. Check triggers
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_statement
FROM information_schema.triggers 
WHERE event_object_schema = 'public' 
AND event_object_table IN ('profiles', 'students', 'teachers', 'subjects');

-- 7. Check indexes
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'students', 'teachers', 'subjects')
ORDER BY tablename, indexname;

-- 8. Check foreign key constraints
SELECT 
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_schema = 'public'
AND tc.table_name IN ('profiles', 'students', 'teachers', 'subjects');

-- 9. Check sample data
SELECT 'profiles' as table_name, count(*) as row_count FROM profiles
UNION ALL
SELECT 'students' as table_name, count(*) as row_count FROM students
UNION ALL
SELECT 'teachers' as table_name, count(*) as row_count FROM teachers
UNION ALL
SELECT 'subjects' as table_name, count(*) as row_count FROM subjects;

-- 10. Test basic operations (this will help identify permission issues)
-- Try to insert a test profile (this should fail due to RLS, but we can see the error)
DO $$
BEGIN
    -- This is just a test - it should fail due to RLS
    INSERT INTO profiles (id, email, full_name, role) 
    VALUES ('00000000-0000-0000-0000-000000000000', '<EMAIL>', 'Test User', 'student');
    
    RAISE NOTICE 'Test insert succeeded (unexpected)';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Test insert failed as expected: %', SQLERRM;
END $$;

-- 11. Check auth schema access (this might fail, which is normal)
DO $$
BEGIN
    PERFORM count(*) FROM auth.users LIMIT 1;
    RAISE NOTICE 'Auth schema accessible';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Auth schema not accessible from this context: %', SQLERRM;
END $$;

-- 12. Final summary
SELECT 
    'Database verification completed' as status,
    current_timestamp as checked_at;
