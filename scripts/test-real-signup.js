#!/usr/bin/env node

/**
 * Test real signup flow through Supabase Auth
 * This tests the actual signup process that users will experience
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testDatabaseTables() {
  console.log('🔍 Testing database tables...')
  
  const tables = ['profiles', 'students', 'teachers', 'subjects']
  let allGood = true
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('count')
        .limit(1)
      
      if (error) {
        console.log(`❌ Table '${table}': ${error.message}`)
        allGood = false
      } else {
        console.log(`✅ Table '${table}': accessible`)
      }
    } catch (error) {
      console.log(`❌ Table '${table}': ${error.message}`)
      allGood = false
    }
  }
  
  return allGood
}

async function testSubjectsData() {
  console.log('\n🔍 Testing subjects data...')
  
  try {
    const { data, error } = await supabase
      .from('subjects')
      .select('id, name, category')
      .eq('is_active', true)
      .limit(5)
    
    if (error) {
      console.error('❌ Subjects query failed:', error.message)
      return false
    }
    
    if (!data || data.length === 0) {
      console.log('⚠️  No subjects found - run the database setup script')
      return false
    }
    
    console.log(`✅ Found ${data.length} subjects:`)
    data.forEach(subject => {
      console.log(`   - ${subject.name} (${subject.category})`)
    })
    
    return true
  } catch (error) {
    console.error('❌ Subjects test error:', error.message)
    return false
  }
}

async function testActualSignup() {
  console.log('\n🧪 Testing actual signup flow...')
  
  // Generate a unique test email with proper format
  const timestamp = Date.now()
  const testEmail = `edubridge.test.${timestamp}@gmail.com`
  const testPassword = 'TestPassword123!'
  
  console.log(`📧 Testing with email: ${testEmail}`)
  
  try {
    // Attempt signup
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: 'Test User',
          role: 'student',
          email: testEmail
        },
        emailRedirectTo: 'http://localhost:3000/auth/confirm'
      }
    })
    
    if (error) {
      console.error('❌ Signup failed:', error.message)
      console.error('Error code:', error.status)
      console.error('Error details:', error)
      return false
    }
    
    console.log('✅ Signup request successful!')
    
    if (data.user) {
      console.log(`📧 User created with ID: ${data.user.id}`)
      console.log(`📧 Email: ${data.user.email}`)
      console.log(`📧 Email confirmed: ${data.user.email_confirmed_at ? 'Yes' : 'No (check email)'}`)
      console.log(`📧 User metadata:`, data.user.user_metadata)
      
      // Wait a moment for the database trigger to execute
      console.log('\n⏳ Waiting for auto-profile creation...')
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Check if profile was automatically created by the trigger
      console.log('🔍 Checking if profile was auto-created...')
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .maybeSingle()
      
      if (profileError) {
        console.error('❌ Profile check failed:', profileError.message)
        return false
      }
      
      if (profile) {
        console.log('🎉 Profile auto-created successfully!')
        console.log(`   - ID: ${profile.id}`)
        console.log(`   - Email: ${profile.email}`)
        console.log(`   - Name: ${profile.full_name}`)
        console.log(`   - Role: ${profile.role}`)
        console.log(`   - Created: ${profile.created_at}`)
        
        // Clean up - delete the test user (optional)
        console.log('\n🧹 Cleaning up test user...')
        const { error: signOutError } = await supabase.auth.signOut()
        if (signOutError) {
          console.log('⚠️  Could not sign out test user:', signOutError.message)
        }
        
        return true
      } else {
        console.log('❌ No profile found - auto-creation trigger may not be working')
        console.log('💡 This means the database trigger needs to be set up')
        return false
      }
    } else {
      console.log('❌ No user data returned from signup')
      return false
    }
    
  } catch (error) {
    console.error('❌ Signup test failed with exception:', error.message)
    return false
  }
}

async function main() {
  console.log('🚀 EduBridge Real Signup Flow Test\n')
  
  // Test database connectivity
  const dbOk = await testDatabaseTables()
  if (!dbOk) {
    console.log('\n❌ Database connectivity issues detected.')
    console.log('🔧 Please run the database setup script first.')
    return
  }
  
  // Test subjects data
  const subjectsOk = await testSubjectsData()
  if (!subjectsOk) {
    console.log('\n❌ Subjects data missing.')
    console.log('🔧 Please run the database setup script to add default subjects.')
    return
  }
  
  // Test actual signup
  console.log('\n⚠️  About to test real signup - this will create a test user')
  console.log('Press Ctrl+C to cancel, or wait 3 seconds to proceed...')
  await new Promise(resolve => setTimeout(resolve, 3000))
  
  const signupOk = await testActualSignup()
  
  console.log('\n' + '='.repeat(60))
  console.log('📊 FINAL TEST RESULTS')
  console.log('='.repeat(60))
  
  if (signupOk) {
    console.log('🎉 ALL TESTS PASSED!')
    console.log('\n✅ Your signup flow is working correctly!')
    console.log('\n📋 Next steps:')
    console.log('1. Try signing up with your real email at http://localhost:3000/auth/signup')
    console.log('2. Check your email for the confirmation link')
    console.log('3. Click the link to complete the signup process')
    console.log('4. Complete your profile setup')
  } else {
    console.log('❌ SIGNUP TEST FAILED')
    console.log('\n🔧 To fix this:')
    console.log('1. Go to your Supabase dashboard')
    console.log('2. Open the SQL Editor')
    console.log('3. Run the script: scripts/simple-database-fix.sql')
    console.log('4. Check Authentication settings:')
    console.log('   - Enable email confirmations')
    console.log('   - Set site URL to http://localhost:3000')
    console.log('   - Add redirect URL: http://localhost:3000/auth/confirm')
    console.log('5. Run this test again')
  }
}

main().catch(error => {
  console.error('💥 Test runner failed:', error)
  process.exit(1)
})
