#!/usr/bin/env node

/**
 * Test signup after database fix
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testDatabaseSetup() {
  console.log('🔍 Testing database setup...')
  
  try {
    // Test profiles table
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1)
    
    if (profileError) {
      console.error('❌ Profiles table error:', profileError.message)
      return false
    }
    console.log('✅ Profiles table accessible')
    
    // Test subjects table
    const { data: subjects, error: subjectError } = await supabase
      .from('subjects')
      .select('id, name')
      .limit(3)
    
    if (subjectError) {
      console.error('❌ Subjects table error:', subjectError.message)
      return false
    }
    
    if (!subjects || subjects.length === 0) {
      console.error('❌ No subjects found - database may not be properly set up')
      return false
    }
    
    console.log(`✅ Subjects table accessible with ${subjects.length} subjects`)
    
    return true
  } catch (error) {
    console.error('❌ Database test failed:', error.message)
    return false
  }
}

async function testSignup() {
  console.log('\n🧪 Testing signup flow...')
  
  const testEmail = `test-${Date.now()}@example.com`
  const testPassword = 'testpassword123'
  
  console.log(`📧 Using test email: ${testEmail}`)
  
  try {
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: 'Test User',
          role: 'student',
          email: testEmail
        },
        emailRedirectTo: 'http://localhost:3000/auth/confirm'
      }
    })
    
    if (error) {
      console.error('❌ Signup failed:', error.message)
      console.error('Error details:', error)
      return false
    }
    
    console.log('✅ Signup successful!')
    console.log(`📧 User ID: ${data.user?.id}`)
    console.log(`📧 Email confirmed: ${data.user?.email_confirmed_at ? 'Yes' : 'No'}`)
    
    // Wait a moment for the trigger to execute
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Check if profile was automatically created
    if (data.user) {
      console.log('\n🔍 Checking if profile was auto-created...')
      
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .maybeSingle()
      
      if (profileError) {
        console.error('❌ Profile check failed:', profileError.message)
      } else if (profile) {
        console.log('✅ Profile auto-created successfully!')
        console.log(`   - Email: ${profile.email}`)
        console.log(`   - Name: ${profile.full_name}`)
        console.log(`   - Role: ${profile.role}`)
      } else {
        console.log('⚠️  No profile found - trigger may not be working')
      }
    }
    
    return true
  } catch (error) {
    console.error('❌ Signup test failed:', error.message)
    return false
  }
}

async function main() {
  console.log('🚀 Testing EduBridge Signup After Database Fix\n')
  
  // Test database setup first
  const dbOk = await testDatabaseSetup()
  if (!dbOk) {
    console.log('\n❌ Database setup test failed.')
    console.log('\n🔧 To fix this:')
    console.log('1. Go to your Supabase dashboard')
    console.log('2. Open the SQL Editor')
    console.log('3. Copy and paste the content from scripts/fix-signup-database-error.sql')
    console.log('4. Run the script')
    console.log('5. Run this test again')
    return
  }
  
  // Test signup
  const signupOk = await testSignup()
  
  if (signupOk) {
    console.log('\n🎉 All tests passed!')
    console.log('\n📋 Next steps:')
    console.log('1. Try the signup flow in your browser at http://localhost:3000/auth/signup')
    console.log('2. Use a real email address to test email confirmation')
    console.log('3. Check your email and click the confirmation link')
    console.log('4. Complete the profile setup process')
  } else {
    console.log('\n❌ Signup test failed.')
    console.log('\n🔧 If the issue persists:')
    console.log('1. Check your Supabase project settings')
    console.log('2. Verify that email authentication is enabled')
    console.log('3. Check the Auth settings in your Supabase dashboard')
    console.log('4. Make sure the site URL is set to http://localhost:3000')
  }
}

main().catch(error => {
  console.error('💥 Test failed:', error)
  process.exit(1)
})
