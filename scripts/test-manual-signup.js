#!/usr/bin/env node

/**
 * Manual signup test to debug the flow
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testSignup() {
  const testEmail = `test-${Date.now()}@example.com`
  const testPassword = 'testpassword123'
  
  console.log('🧪 Testing signup with:', testEmail)
  
  try {
    // Test signup
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: 'Test User',
          role: 'student',
          email: testEmail
        },
        emailRedirectTo: 'http://localhost:3000/auth/confirm'
      }
    })
    
    if (error) {
      console.error('❌ Signup failed:', error.message)
      return false
    }
    
    console.log('✅ Signup successful!')
    console.log('📧 User ID:', data.user?.id)
    console.log('📧 Email confirmed:', data.user?.email_confirmed_at ? 'Yes' : 'No')
    console.log('📧 Session:', data.session ? 'Created' : 'Not created')
    
    if (data.user && !data.user.email_confirmed_at) {
      console.log('📬 Check your email for confirmation link')
      console.log('🔗 Confirmation URL should redirect to: http://localhost:3000/auth/confirm')
    }
    
    // Test if profile was created
    if (data.user) {
      console.log('\n🔍 Checking if profile was created...')
      
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .maybeSingle()
      
      if (profileError) {
        console.error('❌ Profile check failed:', profileError.message)
      } else if (profile) {
        console.log('✅ Profile exists:', profile)
      } else {
        console.log('⚠️  No profile found - this is expected before email confirmation')
      }
    }
    
    return true
  } catch (error) {
    console.error('❌ Signup test failed:', error.message)
    return false
  }
}

async function testDatabaseTables() {
  console.log('\n🔍 Testing database tables...')
  
  const tables = ['profiles', 'students', 'teachers', 'subjects']
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('count')
        .limit(1)
      
      if (error) {
        console.log(`❌ Table '${table}': ${error.message}`)
      } else {
        console.log(`✅ Table '${table}': accessible`)
      }
    } catch (error) {
      console.log(`❌ Table '${table}': ${error.message}`)
    }
  }
}

async function testSubjects() {
  console.log('\n🔍 Testing subjects data...')
  
  try {
    const { data, error } = await supabase
      .from('subjects')
      .select('id, name, category')
      .eq('is_active', true)
      .limit(5)
    
    if (error) {
      console.error('❌ Subjects query failed:', error.message)
      return false
    }
    
    if (!data || data.length === 0) {
      console.log('⚠️  No subjects found - you may need to run the database setup script')
      return false
    }
    
    console.log(`✅ Found ${data.length} subjects:`)
    data.forEach(subject => {
      console.log(`   - ${subject.name} (${subject.category})`)
    })
    
    return true
  } catch (error) {
    console.error('❌ Subjects test error:', error.message)
    return false
  }
}

async function main() {
  console.log('🚀 EduBridge Manual Signup Test\n')
  
  // Test database connectivity first
  await testDatabaseTables()
  await testSubjects()
  
  // Ask user if they want to proceed with signup test
  console.log('\n❓ Do you want to test the signup flow? (This will create a test user)')
  console.log('   You can delete the test user later if needed.')
  console.log('   Press Ctrl+C to cancel, or wait 5 seconds to proceed...')
  
  await new Promise(resolve => setTimeout(resolve, 5000))
  
  console.log('\n📝 Proceeding with signup test...')
  
  const success = await testSignup()
  
  if (success) {
    console.log('\n🎉 Signup test completed successfully!')
    console.log('\n📋 Next steps:')
    console.log('1. Check your email for the confirmation link')
    console.log('2. Click the link to test the confirmation flow')
    console.log('3. Complete the profile setup')
    console.log('4. Check if you can access the dashboard')
  } else {
    console.log('\n❌ Signup test failed. Check the errors above.')
  }
}

main().catch(error => {
  console.error('💥 Test failed:', error)
  process.exit(1)
})
