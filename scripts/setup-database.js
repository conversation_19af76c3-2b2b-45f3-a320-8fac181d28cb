-- EduBridge Database Schema
-- Run these queries in your Supabase SQL editor

-- PART 1: Extensions and Types
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('student', 'teacher', 'admin');
CREATE TYPE board_type AS ENUM ('punjab', 'sindh', 'kpk', 'federal', 'balochistan');
CREATE TYPE province_type AS ENUM ('punjab', 'sindh', 'kpk', 'balochistan', 'islamabad');
CREATE TYPE subscription_status AS ENUM ('active', 'cancelled', 'expired');
CREATE TYPE payment_status AS ENUM ('completed', 'pending', 'failed');

-- PART 2: Core Tables
-- Profiles table (extends auth.users)
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    role user_role NOT NULL DEFAULT 'student',
    phone TEXT,
    date_of_birth DATE,
    city TEXT,
    province province_type,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Students table
CREATE TABLE students (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    class_level INTEGER NOT NULL CHECK (class_level >= 5 AND class_level <= 12),
    board board_type NOT NULL,
    subjects TEXT[] DEFAULT '{}',
    parent_name TEXT,
    parent_phone TEXT,
    parent_email TEXT,
    learning_goals TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Teachers table
CREATE TABLE teachers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    qualifications TEXT[] DEFAULT '{}',
    experience_years INTEGER DEFAULT 0,
    subjects TEXT[] DEFAULT '{}',
    class_levels INTEGER[] DEFAULT '{}',
    bio TEXT,
    hourly_rate DECIMAL(10,2) DEFAULT 0.00,
    is_verified BOOLEAN DEFAULT false,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_students INTEGER DEFAULT 0,
    total_hours_taught INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Courses table
CREATE TABLE courses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    teacher_id UUID REFERENCES teachers(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    subject TEXT NOT NULL,
    class_level INTEGER NOT NULL CHECK (class_level >= 5 AND class_level <= 12),
    board board_type NOT NULL,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    duration_weeks INTEGER DEFAULT 12,
    max_students INTEGER DEFAULT 30,
    current_students INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- PART 3: Subscription and Payment Tables
-- Subscriptions table
CREATE TABLE subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    student_id UUID REFERENCES students(id) ON DELETE CASCADE NOT NULL,
    teacher_id UUID REFERENCES teachers(id) ON DELETE CASCADE NOT NULL,
    status subscription_status NOT NULL DEFAULT 'active',
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    monthly_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Course purchases table
CREATE TABLE course_purchases (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    student_id UUID REFERENCES students(id) ON DELETE CASCADE NOT NULL,
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE NOT NULL,
    amount_paid DECIMAL(10,2) NOT NULL,
    payment_status payment_status NOT NULL DEFAULT 'pending',
    purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- PART 4: Triggers for updated_at
-- Function to update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for all tables
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON students
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teachers_updated_at BEFORE UPDATE ON teachers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_course_purchases_updated_at BEFORE UPDATE ON course_purchases
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- PART 5: Row Level Security (RLS)
-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_purchases ENABLE ROW LEVEL SECURITY;

-- RLS Policies for profiles
CREATE POLICY "Public profiles are viewable by everyone" ON profiles
    FOR SELECT USING (true);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS Policies for students
CREATE POLICY "Students are viewable by everyone" ON students
    FOR SELECT USING (true);

CREATE POLICY "Users can manage own student profile" ON students
    FOR ALL USING (auth.uid() = profile_id);

-- RLS Policies for teachers
CREATE POLICY "Teachers are viewable by everyone" ON teachers
    FOR SELECT USING (true);

CREATE POLICY "Users can manage own teacher profile" ON teachers
    FOR ALL USING (auth.uid() = profile_id);

-- RLS Policies for courses
CREATE POLICY "Courses are viewable by everyone" ON courses
    FOR SELECT USING (true);

CREATE POLICY "Teachers can manage own courses" ON courses
    FOR ALL USING (
        auth.uid() IN (
            SELECT profile_id FROM teachers WHERE id = teacher_id
        )
    );

-- RLS Policies for subscriptions
CREATE POLICY "Users can view own subscriptions" ON subscriptions
    FOR SELECT USING (
        auth.uid() IN (
            SELECT profile_id FROM students WHERE id = student_id
        ) OR auth.uid() IN (
            SELECT profile_id FROM teachers WHERE id = teacher_id
        )
    );

CREATE POLICY "Students can manage own subscriptions" ON subscriptions
    FOR ALL USING (
        auth.uid() IN (
            SELECT profile_id FROM students WHERE id = student_id
        )
    );

-- RLS Policies for course_purchases
CREATE POLICY "Users can view own course purchases" ON course_purchases
    FOR SELECT USING (
        auth.uid() IN (
            SELECT profile_id FROM students WHERE id = student_id
        )
    );

CREATE POLICY "Students can manage own course purchases" ON course_purchases
    FOR ALL USING (
        auth.uid() IN (
            SELECT profile_id FROM students WHERE id = student_id
        )
    );

-- PART 6: Indexes for Performance
-- Create indexes for better query performance
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_students_profile_id ON students(profile_id);
CREATE INDEX idx_students_class_level ON students(class_level);
CREATE INDEX idx_students_board ON students(board);
CREATE INDEX idx_teachers_profile_id ON teachers(profile_id);
CREATE INDEX idx_teachers_subjects ON teachers USING GIN(subjects);
CREATE INDEX idx_teachers_class_levels ON teachers USING GIN(class_levels);
CREATE INDEX idx_teachers_is_verified ON teachers(is_verified);
CREATE INDEX idx_courses_teacher_id ON courses(teacher_id);
CREATE INDEX idx_courses_subject ON courses(subject);
CREATE INDEX idx_courses_class_level ON courses(class_level);
CREATE INDEX idx_courses_board ON courses(board);
CREATE INDEX idx_courses_is_active ON courses(is_active);
CREATE INDEX idx_subscriptions_student_id ON subscriptions(student_id);
CREATE INDEX idx_subscriptions_teacher_id ON subscriptions(teacher_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_course_purchases_student_id ON course_purchases(student_id);
CREATE INDEX idx_course_purchases_course_id ON course_purchases(course_id);
CREATE INDEX idx_course_purchases_payment_status ON course_purchases(payment_status);