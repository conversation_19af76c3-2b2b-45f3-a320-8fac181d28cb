-- Fix for "Database error saving new user" issue
-- This script addresses common causes of signup failures in Supabase

-- 1. First, let's check what's causing the issue
SELECT 'Starting database fix for signup errors...' as status;

-- 2. Ensure UUID extension is available
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 3. Create or recreate the profiles table with proper structure
-- Drop and recreate to ensure clean state
DROP TABLE IF EXISTS public.profiles CASCADE;

-- <PERSON>reate profiles table that properly extends auth.users
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    role TEXT NOT NULL DEFAULT 'student' CHECK (role IN ('student', 'teacher', 'admin')),
    phone TEXT,
    date_of_birth DATE,
    city TEXT,
    province TEXT CHECK (province IN ('punjab', 'sindh', 'kpk', 'balochistan', 'islamabad')),
    gender TEXT CHECK (gender IN ('male', 'female', 'other')),
    address TEXT,
    emergency_contact TEXT,
    emergency_phone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create a function to automatically create profiles for new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'role', 'student')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create trigger to automatically create profile when user signs up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 6. Enable RLS on profiles table
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 7. Create RLS policies that allow proper access
-- Drop existing policies first
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;

-- Create new policies
CREATE POLICY "Enable read access for all users" ON public.profiles
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Enable update for users based on id" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- 8. Create students table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.students (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    class_level INTEGER NOT NULL CHECK (class_level >= 5 AND class_level <= 12),
    board TEXT NOT NULL CHECK (board IN ('punjab', 'sindh', 'kpk', 'federal', 'balochistan')),
    subjects TEXT[] DEFAULT '{}',
    parent_name TEXT,
    parent_phone TEXT,
    parent_email TEXT,
    learning_goals TEXT,
    preferred_subjects TEXT[] DEFAULT '{}',
    weak_subjects TEXT[] DEFAULT '{}',
    study_goals TEXT,
    preferred_study_time TEXT CHECK (preferred_study_time IN ('morning', 'afternoon', 'evening', 'night', 'flexible')),
    budget_range TEXT CHECK (budget_range IN ('under_1000', '1000_3000', '3000_5000', '5000_10000', 'above_10000')),
    learning_style TEXT CHECK (learning_style IN ('visual', 'auditory', 'kinesthetic', 'reading_writing', 'mixed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. Create teachers table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.teachers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    qualifications TEXT[] DEFAULT '{}',
    experience_years INTEGER DEFAULT 0,
    subjects TEXT[] DEFAULT '{}',
    class_levels INTEGER[] DEFAULT '{}',
    bio TEXT,
    hourly_rate DECIMAL(10,2) DEFAULT 0.00,
    is_verified BOOLEAN DEFAULT false,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_students INTEGER DEFAULT 0,
    total_hours_taught INTEGER DEFAULT 0,
    specialization TEXT,
    education_level TEXT,
    university TEXT,
    graduation_year TEXT,
    cnic TEXT,
    teaching_experience_description TEXT,
    teaching_style TEXT,
    languages TEXT[] DEFAULT '{}',
    availability_schedule JSONB DEFAULT '{}',
    min_hourly_rate DECIMAL(10,2) DEFAULT 0.00,
    max_hourly_rate DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. Create subjects table with default data
CREATE TABLE IF NOT EXISTS public.subjects (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    category TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 11. Insert default subjects (using simple text categories, not enums)
INSERT INTO public.subjects (name, category, description, is_active)
VALUES
    ('Mathematics', 'mathematics', 'Mathematics for all levels', true),
    ('Additional Mathematics', 'mathematics', 'Advanced Mathematics', true),
    ('Physics', 'physics', 'Physics for all levels', true),
    ('Chemistry', 'chemistry', 'Chemistry for all levels', true),
    ('Biology', 'biology', 'Biology for all levels', true),
    ('English', 'language', 'English language and literature', true),
    ('Urdu', 'language', 'Urdu language and literature', true),
    ('Computer Science', 'technology', 'Computer Science and Programming', true),
    ('Economics', 'social_science', 'Economics for all levels', true),
    ('Accounting', 'commerce', 'Accounting and Finance', true),
    ('Business Studies', 'commerce', 'Business Studies', true),
    ('History', 'social_science', 'History and Social Studies', true),
    ('Geography', 'social_science', 'Geography', true),
    ('Islamic Studies', 'religious', 'Islamic Studies', true),
    ('Pakistan Studies', 'social_science', 'Pakistan Studies', true)
ON CONFLICT (name) DO NOTHING;

-- 12. Enable RLS on other tables
ALTER TABLE public.students ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subjects ENABLE ROW LEVEL SECURITY;

-- 13. Create RLS policies for students
DROP POLICY IF EXISTS "Students can view own data" ON public.students;
DROP POLICY IF EXISTS "Students can insert own data" ON public.students;
DROP POLICY IF EXISTS "Students can update own data" ON public.students;

CREATE POLICY "Students can view own data" ON public.students
    FOR SELECT USING (auth.uid() = profile_id);

CREATE POLICY "Students can insert own data" ON public.students
    FOR INSERT WITH CHECK (auth.uid() = profile_id);

CREATE POLICY "Students can update own data" ON public.students
    FOR UPDATE USING (auth.uid() = profile_id);

-- 14. Create RLS policies for teachers
DROP POLICY IF EXISTS "Teachers can view own data" ON public.teachers;
DROP POLICY IF EXISTS "Teachers can insert own data" ON public.teachers;
DROP POLICY IF EXISTS "Teachers can update own data" ON public.teachers;

CREATE POLICY "Teachers can view own data" ON public.teachers
    FOR SELECT USING (auth.uid() = profile_id);

CREATE POLICY "Teachers can insert own data" ON public.teachers
    FOR INSERT WITH CHECK (auth.uid() = profile_id);

CREATE POLICY "Teachers can update own data" ON public.teachers
    FOR UPDATE USING (auth.uid() = profile_id);

-- 15. Create RLS policies for subjects (public read)
DROP POLICY IF EXISTS "Everyone can view subjects" ON public.subjects;
CREATE POLICY "Everyone can view subjects" ON public.subjects
    FOR SELECT USING (true);

-- 16. Create updated_at trigger function
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 17. Create triggers for updated_at
DROP TRIGGER IF EXISTS update_profiles_updated_at ON public.profiles;
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_students_updated_at ON public.students;
CREATE TRIGGER update_students_updated_at 
    BEFORE UPDATE ON public.students
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_teachers_updated_at ON public.teachers;
CREATE TRIGGER update_teachers_updated_at 
    BEFORE UPDATE ON public.teachers
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- 18. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
CREATE INDEX IF NOT EXISTS idx_students_profile_id ON public.students(profile_id);
CREATE INDEX IF NOT EXISTS idx_teachers_profile_id ON public.teachers(profile_id);

-- 19. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- 20. Final verification
SELECT 
    'Database setup completed successfully!' as message,
    'Profiles table created with auto-trigger' as profiles_status,
    'RLS policies configured' as security_status,
    'Default subjects inserted' as subjects_status,
    current_timestamp as completed_at;

-- Test the trigger by showing it exists
SELECT 
    trigger_name,
    event_object_table,
    action_statement
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';
