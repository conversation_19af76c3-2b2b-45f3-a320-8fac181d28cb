// Test script to verify the new authentication flow
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://yscalbrckcblxdmmiohy.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlzY2FsYnJja2NibHhkbW1pb2h5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1NjM4NjQsImV4cCI6MjA2NTEzOTg2NH0.5XCoqiYAfKf6BxrT_sXLclXa3QckeW_Pq3HaAtKccDI'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...')
  
  try {
    // Test basic connection
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1)
    
    if (error) {
      console.error('❌ Database connection failed:', error.message)
      return false
    }
    
    console.log('✅ Database connection successful')
    return true
  } catch (error) {
    console.error('❌ Database connection error:', error.message)
    return false
  }
}

async function testAuthFlow() {
  console.log('🔍 Testing authentication flow...')
  
  try {
    // Test signup (this will fail if user exists, which is expected)
    const testEmail = `test-${Date.now()}@example.com`
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: 'testpassword123',
      options: {
        data: {
          full_name: 'Test User',
          role: 'student'
        }
      }
    })
    
    if (error && !error.message.includes('already registered')) {
      console.error('❌ Auth signup failed:', error.message)
      return false
    }
    
    console.log('✅ Auth signup flow working')
    return true
  } catch (error) {
    console.error('❌ Auth flow error:', error.message)
    return false
  }
}

async function runTests() {
  console.log('🚀 Starting EduBridge authentication tests...\n')
  
  const dbTest = await testDatabaseConnection()
  const authTest = await testAuthFlow()
  
  console.log('\n📊 Test Results:')
  console.log(`Database Connection: ${dbTest ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Authentication Flow: ${authTest ? '✅ PASS' : '❌ FAIL'}`)
  
  if (dbTest && authTest) {
    console.log('\n🎉 All tests passed! Your authentication setup is working correctly.')
    console.log('\n📝 Next steps:')
    console.log('1. Run the SQL schema in your Supabase dashboard')
    console.log('2. Start your development server: npm run dev')
    console.log('3. Test the signup flow in your browser')
  } else {
    console.log('\n⚠️  Some tests failed. Please check your Supabase configuration.')
  }
}

runTests().catch(console.error)
