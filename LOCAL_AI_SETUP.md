# 🤖 Local Llama 3.1-8B Setup for EduBridge

## 🎯 What We're Setting Up

- **Model:** Llama 3.1-8B Instruct (Meta's latest)
- **Size:** ~5GB download
- **Quality:** Excellent for tutoring and education
- **Cost:** 100% FREE - no API costs ever!
- **Speed:** 2-3 seconds response time on modern hardware

## 📋 Prerequisites

- **RAM:** At least 8GB (16GB recommended)
- **Storage:** 10GB free space
- **OS:** macOS, Linux, or Windows
- **Internet:** For initial model download

## 🚀 Step 1: Install Ollama

### macOS:
```bash
brew install ollama
```

### Linux:
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

### Windows:
1. Download from https://ollama.ai/download
2. Run the installer
3. Restart your terminal

## 🚀 Step 2: Download Llama 3.1-8B

```bash
# Download the model (this will take a few minutes)
ollama pull llama3.1:8b

# Verify the model is downloaded
ollama list
```

**Expected output:**
```
NAME            ID              SIZE    MODIFIED
llama3.1:8b     42182c40c2f7    4.7GB   2 minutes ago
```

## 🚀 Step 3: Start Ollama Server

```bash
# Start the Ollama server (keep this running)
ollama serve
```

**Expected output:**
```
2024/01/09 10:30:00 routes.go:777: INFO server config env="map[OLLAMA_DEBUG:false ...]"
2024/01/09 10:30:00 routes.go:784: INFO inferencing at http://127.0.0.1:11434
```

## 🚀 Step 4: Test the Model

Open a new terminal and test:

```bash
# Test basic functionality
ollama run llama3.1:8b "Explain quadratic equations for a Pakistani Class 10 student"
```

**Expected response:**
The model should give a detailed explanation about quadratic equations in educational context.

## 🚀 Step 5: Set Up EduBridge Database

1. **Go to your Supabase project:** https://cosmaeoxoprxbacgacwr.supabase.co
2. **Open SQL Editor**
3. **Copy and paste the entire content from `database/schema.sql`**
4. **Click "Run"**

This will create all the necessary tables for subscriptions, users, courses, etc.

## 🚀 Step 6: Get Supabase Service Role Key

1. **Go to:** https://cosmaeoxoprxbacgacwr.supabase.co/project/settings/api
2. **Copy the "service_role" key** (not the anon key)
3. **Update your `.env.local`:**

```env
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## 🚀 Step 7: Test EduBridge

```bash
# Start the development server
npm run dev
```

**Test the AI:**
1. Open http://localhost:3000
2. Go to http://localhost:3000/demo/subscriptions
3. Try the subscription flow
4. Test AI tutoring features

## 🔧 Troubleshooting

### Problem: "Connection refused" error
**Solution:** Make sure Ollama server is running:
```bash
ollama serve
```

### Problem: "Model not found" error
**Solution:** Download the model:
```bash
ollama pull llama3.1:8b
```

### Problem: Slow responses
**Solutions:**
- Close other applications to free up RAM
- Use smaller model: `ollama pull llama3.1:7b`
- Upgrade hardware (more RAM/better CPU)

### Problem: Model gives poor responses
**Solutions:**
- Try different prompts
- Adjust temperature in the code (lower = more focused)
- Use the larger model: `ollama pull llama3.1:70b` (if you have 64GB+ RAM)

## 🎯 Model Performance

### Hardware Requirements:
- **Minimum:** 8GB RAM, 4-core CPU
- **Recommended:** 16GB RAM, 8-core CPU
- **Optimal:** 32GB RAM, 16-core CPU

### Expected Response Times:
- **8GB RAM:** 5-10 seconds
- **16GB RAM:** 2-5 seconds  
- **32GB+ RAM:** 1-3 seconds

## 🔄 Alternative Models

If Llama 3.1-8B is too slow, try these smaller models:

```bash
# Faster but smaller models
ollama pull mistral:7b        # Good balance
ollama pull phi3:mini         # Very fast, smaller
ollama pull gemma2:2b         # Fastest, basic

# Update your .env.local:
AI_MODEL=mistral:7b
```

## 🎓 Why Llama 3.1-8B is Perfect for Tutoring

✅ **Educational Excellence:** Trained on educational content  
✅ **Math Proficiency:** Excellent at step-by-step problem solving  
✅ **Multilingual:** Supports Urdu and English  
✅ **Curriculum Aware:** Can adapt to Pakistani education system  
✅ **Explanation Skills:** Great at breaking down complex concepts  
✅ **Cultural Context:** Understands Pakistani context  

## 🚀 Next Steps

1. **Complete the setup above**
2. **Test the AI tutoring features**
3. **Create teacher and student accounts**
4. **Try the subscription system**
5. **Explore the AI-generated quizzes and flashcards**

## 💡 Pro Tips

- **Keep Ollama running:** Start it when you boot your computer
- **Monitor resources:** Use Activity Monitor/Task Manager to check RAM usage
- **Experiment with prompts:** The AI responds better to specific, detailed questions
- **Use context:** Mention the student's class level and board for better responses

Your EduBridge platform is now ready with a powerful, free, local AI tutor! 🎉
