{"name": "edubridge", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.50.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "dotenv": "^16.5.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next": "14.0.4", "react": "^18", "react-dom": "^18", "sonner": "^2.0.5", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}