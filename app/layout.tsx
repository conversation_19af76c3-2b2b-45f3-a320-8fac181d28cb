import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ToastProvider } from '@/components/providers/toast-provider'
import { AuthProvider } from '@/components/providers/auth-provider'
import { AuthRedirectHandler } from '@/components/auth/redirect-handler'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'EduBridge - Online Learning & Teacher Marketplace for Pakistan',
  description: 'AI-powered education platform connecting Pakistani students with qualified teachers. Learn with curriculum-aligned content, AI tutoring, and live classes.',
  keywords: 'Pakistan education, online learning, AI tutor, Pakistani curriculum, teachers marketplace',
  authors: [{ name: 'EduBridge Team' }],
  creator: 'EduB<PERSON>',
  publisher: 'EduBridge',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  openGraph: {
    title: 'EduBridge - Online Learning & Teacher Marketplace for Pakistan',
    description: 'AI-powered education platform connecting Pakistani students with qualified teachers.',
    url: '/',
    siteName: 'EduBridge',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'EduBridge - Online Learning Platform',
      },
    ],
    locale: 'en_PK',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'EduBridge - Online Learning & Teacher Marketplace for Pakistan',
    description: 'AI-powered education platform connecting Pakistani students with qualified teachers.',
    images: ['/og-image.png'],
    creator: '@edubridge_pk',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <AuthProvider>
          <AuthRedirectHandler />
          <div className="relative flex min-h-screen flex-col">
            <div className="flex-1">{children}</div>
          </div>
          <ToastProvider />
        </AuthProvider>
      </body>
    </html>
  )
}
