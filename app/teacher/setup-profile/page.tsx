'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/components/providers/auth-provider'
import { Loader2, GraduationCap, BookOpen, Clock, DollarSign, Award, MapPin, Languages } from 'lucide-react'

interface Subject {
  id: string
  name: string
  category: string
  description: string
}

export default function TeacherSetupProfilePage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [formData, setFormData] = useState({
    // Basic Info
    specialization: '',
    educationLevel: '',
    university: '',
    graduationYear: '',
    cnic: '',
    gender: '',
    address: '',
    
    // Teaching Info
    experienceYears: '',
    teachingExperienceDescription: '',
    qualifications: [] as string[],
    teachingStyle: '',
    languages: [] as string[],
    
    // Subjects & Classes
    selectedSubjects: [] as string[],
    classLevels: [] as number[],
    
    // Availability & Rates
    availabilityDays: [] as string[],
    availabilityTimes: [] as string[],
    minHourlyRate: '',
    maxHourlyRate: '',
    preferredClassSize: '',
    
    // Teaching Modes
    onlineTeaching: true,
    homeTutoring: false,
    instituteTeaching: false,
    
    // Professional Info
    bio: '',
    bankAccount: '',
    jazzcashAccount: ''
  })

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin')
    } else if (!loading && user && user.role !== 'teacher') {
      switch (user.role) {
        case 'student':
          router.push('/student/dashboard')
          break
        case 'admin':
          router.push('/admin/dashboard')
          break
        default:
          router.push('/teacher/dashboard')
      }
    }
  }, [user, loading, router])

  // Load subjects
  useEffect(() => {
    const loadSubjects = async () => {
      const { data, error } = await supabase
        .from('subjects')
        .select('id, name, category, description')
        .eq('is_active', true)
        .order('category, name')

      if (error) {
        console.error('Error loading subjects:', error)
      } else {
        setSubjects(data || [])
      }
    }

    loadSubjects()
  }, [])

  const handleInputChange = (field: string, value: string | number | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleArrayToggle = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: (prev[field as keyof typeof prev] as any[]).includes(value)
        ? (prev[field as keyof typeof prev] as any[]).filter((item: any) => item !== value)
        : [...(prev[field as keyof typeof prev] as any[]), value]
    }))
  }

  const nextStep = () => {
    if (currentStep < 5) setCurrentStep(currentStep + 1)
  }

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      if (!user) {
        toast.error('User not authenticated')
        setIsLoading(false)
        return
      }

      // Validation
      if (!formData.specialization || !formData.experienceYears) {
        toast.error('Please fill in all required fields')
        setIsLoading(false)
        return
      }

      console.log('Creating teacher profile for user:', user.id)

      // First, create the basic profile record
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          email: user.email!,
          full_name: user.user_metadata?.full_name || '',
          role: 'teacher',
          city: formData.address || null
        })

      if (profileError && !profileError.message.includes('duplicate key')) {
        console.error('Profile creation error:', profileError)
        toast.error('Profile Creation Failed', {
          description: profileError.message
        })
        setIsLoading(false)
        return
      }

      // Create teacher profile
      const { error: teacherError } = await supabase
        .from('teachers')
        .insert({
          profile_id: user.id,
          qualifications: [formData.specialization],
          experience_years: parseInt(formData.experienceYears) || 0,
          subjects: formData.selectedSubjects,
          class_levels: formData.classLevels,
          bio: formData.bio || null,
          hourly_rate: parseFloat(formData.minHourlyRate) || 0,
          is_verified: false
        })

      if (teacherError) {
        console.error('Teacher profile creation error:', teacherError)
        toast.error('Teacher Profile Creation Failed', {
          description: teacherError.message
        })
        setIsLoading(false)
        return
      }

      toast.success('Profile Setup Complete!', {
        description: 'Welcome to EduBridge! Redirecting to your dashboard...'
      })

      setTimeout(() => {
        router.push('/teacher/dashboard')
      }, 2000)

    } catch (error) {
      console.error('Profile setup error:', error)
      toast.error('An unexpected error occurred')
      setIsLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-edubridge-50 to-white flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-edubridge-600" />
      </div>
    )
  }

  if (!user || user.role !== 'teacher') {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-edubridge-50 to-white p-4">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="text-center mb-8">
            <GraduationCap className="h-16 w-16 text-edubridge-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900">Complete Your Teacher Profile</h1>
            <p className="text-gray-600 mt-2">
              Help students find you by providing detailed information about your expertise
            </p>
          </div>

          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">Step {currentStep} of 5</span>
              <span className="text-sm text-gray-500">{Math.round((currentStep / 5) * 100)}% Complete</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-edubridge-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(currentStep / 5) * 100}%` }}
              />
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>
                {currentStep === 1 && "Basic Information"}
                {currentStep === 2 && "Education & Experience"}
                {currentStep === 3 && "Subjects & Classes"}
                {currentStep === 4 && "Availability & Rates"}
                {currentStep === 5 && "Professional Details"}
              </CardTitle>
              <CardDescription>
                {currentStep === 1 && "Tell us about yourself"}
                {currentStep === 2 && "Your educational background and teaching experience"}
                {currentStep === 3 && "What subjects and classes can you teach?"}
                {currentStep === 4 && "When are you available and what are your rates?"}
                {currentStep === 5 && "Final details to complete your profile"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Step 1: Basic Information */}
                {currentStep === 1 && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="specialization">Teaching Specialization *</Label>
                        <Input
                          id="specialization"
                          placeholder="e.g., Mathematics, Physics, Chemistry"
                          value={formData.specialization}
                          onChange={(e) => handleInputChange('specialization', e.target.value)}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="experienceYears">Years of Experience *</Label>
                        <Select value={formData.experienceYears} onValueChange={(value) => handleInputChange('experienceYears', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select experience" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="0">Fresh Graduate</SelectItem>
                            <SelectItem value="1">1 Year</SelectItem>
                            <SelectItem value="2">2 Years</SelectItem>
                            <SelectItem value="3">3 Years</SelectItem>
                            <SelectItem value="4">4 Years</SelectItem>
                            <SelectItem value="5">5+ Years</SelectItem>
                            <SelectItem value="10">10+ Years</SelectItem>
                            <SelectItem value="15">15+ Years</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="gender">Gender</Label>
                        <Select value={formData.gender} onValueChange={(value) => handleInputChange('gender', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select gender" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="male">Male</SelectItem>
                            <SelectItem value="female">Female</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="address">City/Address</Label>
                        <Input
                          id="address"
                          placeholder="e.g., Lahore, Karachi, Islamabad"
                          value={formData.address}
                          onChange={(e) => handleInputChange('address', e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="cnic">CNIC Number</Label>
                      <Input
                        id="cnic"
                        placeholder="XXXXX-XXXXXXX-X"
                        value={formData.cnic}
                        onChange={(e) => handleInputChange('cnic', e.target.value)}
                      />
                    </div>
                  </div>
                )}

                {/* Step 2: Education & Experience */}
                {currentStep === 2 && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="educationLevel">Education Level *</Label>
                        <Select value={formData.educationLevel} onValueChange={(value) => handleInputChange('educationLevel', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select education level" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="bachelors">Bachelor's Degree</SelectItem>
                            <SelectItem value="masters">Master's Degree</SelectItem>
                            <SelectItem value="phd">PhD</SelectItem>
                            <SelectItem value="diploma">Diploma</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="university">University/Institution</Label>
                        <Input
                          id="university"
                          placeholder="e.g., University of Punjab, LUMS"
                          value={formData.university}
                          onChange={(e) => handleInputChange('university', e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="graduationYear">Graduation Year</Label>
                        <Input
                          id="graduationYear"
                          type="number"
                          placeholder="e.g., 2020"
                          value={formData.graduationYear}
                          onChange={(e) => handleInputChange('graduationYear', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="teachingStyle">Teaching Style</Label>
                        <Select value={formData.teachingStyle} onValueChange={(value) => handleInputChange('teachingStyle', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select teaching style" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="interactive">Interactive</SelectItem>
                            <SelectItem value="lecture_based">Lecture Based</SelectItem>
                            <SelectItem value="practical">Practical/Hands-on</SelectItem>
                            <SelectItem value="discussion">Discussion Based</SelectItem>
                            <SelectItem value="mixed">Mixed Approach</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="teachingExperienceDescription">Teaching Experience Description</Label>
                      <Textarea
                        id="teachingExperienceDescription"
                        placeholder="Describe your teaching experience, achievements, and methodology..."
                        value={formData.teachingExperienceDescription}
                        onChange={(e) => handleInputChange('teachingExperienceDescription', e.target.value)}
                        rows={4}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Languages You Can Teach In</Label>
                      <div className="grid grid-cols-3 gap-2">
                        {['English', 'Urdu', 'Punjabi', 'Sindhi', 'Pashto', 'Balochi'].map(language => (
                          <div key={language} className="flex items-center space-x-2">
                            <Checkbox
                              id={language}
                              checked={formData.languages.includes(language)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  handleArrayToggle('languages', language)
                                } else {
                                  handleArrayToggle('languages', language)
                                }
                              }}
                            />
                            <Label htmlFor={language} className="text-sm">{language}</Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 3: Subjects & Classes */}
                {currentStep === 3 && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>Classes You Can Teach *</Label>
                      <div className="grid grid-cols-4 gap-2">
                        {[5, 6, 7, 8, 9, 10, 11, 12].map(classLevel => (
                          <div key={classLevel} className="flex items-center space-x-2">
                            <Checkbox
                              id={`class-${classLevel}`}
                              checked={formData.classLevels.includes(classLevel)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  handleArrayToggle('classLevels', classLevel)
                                } else {
                                  handleArrayToggle('classLevels', classLevel)
                                }
                              }}
                            />
                            <Label htmlFor={`class-${classLevel}`} className="text-sm">Class {classLevel}</Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Subjects You Can Teach *</Label>
                      <div className="grid grid-cols-2 gap-2 max-h-60 overflow-y-auto">
                        {subjects.map(subject => (
                          <div key={subject.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={subject.id}
                              checked={formData.selectedSubjects.includes(subject.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  handleArrayToggle('selectedSubjects', subject.id)
                                } else {
                                  handleArrayToggle('selectedSubjects', subject.id)
                                }
                              }}
                            />
                            <Label htmlFor={subject.id} className="text-sm">{subject.name}</Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Teaching Modes</Label>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="onlineTeaching"
                            checked={formData.onlineTeaching}
                            onCheckedChange={(checked) => handleInputChange('onlineTeaching', checked as boolean)}
                          />
                          <Label htmlFor="onlineTeaching">Online Teaching</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="homeTutoring"
                            checked={formData.homeTutoring}
                            onCheckedChange={(checked) => handleInputChange('homeTutoring', checked as boolean)}
                          />
                          <Label htmlFor="homeTutoring">Home Tutoring</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="instituteTeaching"
                            checked={formData.instituteTeaching}
                            onCheckedChange={(checked) => handleInputChange('instituteTeaching', checked as boolean)}
                          />
                          <Label htmlFor="instituteTeaching">Institute Teaching</Label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 4: Availability & Rates */}
                {currentStep === 4 && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>Available Days</Label>
                      <div className="grid grid-cols-4 gap-2">
                        {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map(day => (
                          <div key={day} className="flex items-center space-x-2">
                            <Checkbox
                              id={day}
                              checked={formData.availabilityDays.includes(day)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  handleArrayToggle('availabilityDays', day)
                                } else {
                                  handleArrayToggle('availabilityDays', day)
                                }
                              }}
                            />
                            <Label htmlFor={day} className="text-sm">{day}</Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Available Times</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {['Morning (6AM-12PM)', 'Afternoon (12PM-6PM)', 'Evening (6PM-10PM)', 'Night (10PM-12AM)'].map(time => (
                          <div key={time} className="flex items-center space-x-2">
                            <Checkbox
                              id={time}
                              checked={formData.availabilityTimes.includes(time)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  handleArrayToggle('availabilityTimes', time)
                                } else {
                                  handleArrayToggle('availabilityTimes', time)
                                }
                              }}
                            />
                            <Label htmlFor={time} className="text-sm">{time}</Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="minHourlyRate">Minimum Hourly Rate (PKR) *</Label>
                        <Input
                          id="minHourlyRate"
                          type="number"
                          placeholder="e.g., 500"
                          value={formData.minHourlyRate}
                          onChange={(e) => handleInputChange('minHourlyRate', e.target.value)}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="maxHourlyRate">Maximum Hourly Rate (PKR) *</Label>
                        <Input
                          id="maxHourlyRate"
                          type="number"
                          placeholder="e.g., 2000"
                          value={formData.maxHourlyRate}
                          onChange={(e) => handleInputChange('maxHourlyRate', e.target.value)}
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="preferredClassSize">Preferred Class Size</Label>
                      <Select value={formData.preferredClassSize} onValueChange={(value) => handleInputChange('preferredClassSize', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select preferred class size" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1-on-1 (Individual)</SelectItem>
                          <SelectItem value="2">Small Group (2-3 students)</SelectItem>
                          <SelectItem value="5">Medium Group (4-6 students)</SelectItem>
                          <SelectItem value="10">Large Group (7+ students)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                {/* Step 5: Professional Details */}
                {currentStep === 5 && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="bio">Professional Bio *</Label>
                      <Textarea
                        id="bio"
                        placeholder="Write a compelling bio that highlights your expertise, teaching philosophy, and what makes you unique as a teacher..."
                        value={formData.bio}
                        onChange={(e) => handleInputChange('bio', e.target.value)}
                        rows={6}
                        required
                      />
                      <p className="text-sm text-gray-500">This will be shown to students on your profile</p>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="bankAccount">Bank Account (Optional)</Label>
                        <Input
                          id="bankAccount"
                          placeholder="Bank account details for payments"
                          value={formData.bankAccount}
                          onChange={(e) => handleInputChange('bankAccount', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="jazzcashAccount">JazzCash Account (Optional)</Label>
                        <Input
                          id="jazzcashAccount"
                          placeholder="03XXXXXXXXX"
                          value={formData.jazzcashAccount}
                          onChange={(e) => handleInputChange('jazzcashAccount', e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h4 className="font-medium text-blue-900 mb-2">🎉 Almost Done!</h4>
                      <p className="text-blue-800 text-sm">
                        Your profile will be reviewed by our team for verification. Once approved,
                        you'll be able to start teaching and earning on EduBridge!
                      </p>
                    </div>
                  </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between pt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={prevStep}
                    disabled={currentStep === 1}
                  >
                    Previous
                  </Button>
                  
                  {currentStep < 5 ? (
                    <Button
                      type="button"
                      onClick={nextStep}
                      className="bg-edubridge-600 hover:bg-edubridge-700"
                    >
                      Next
                    </Button>
                  ) : (
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="bg-edubridge-600 hover:bg-edubridge-700"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Setting up...
                        </>
                      ) : (
                        'Complete Setup'
                      )}
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
