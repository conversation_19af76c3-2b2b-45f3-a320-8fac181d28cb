'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'

export default function DebugConfirmPage() {
  const [urlInfo, setUrlInfo] = useState<any>({})
  const searchParams = useSearchParams()

  useEffect(() => {
    // Get all URL information
    const info = {
      fullUrl: window.location.href,
      pathname: window.location.pathname,
      search: window.location.search,
      hash: window.location.hash,
      searchParams: {},
      hashParams: {},
    }

    // Get search params
    const urlParams = new URLSearchParams(window.location.search)
    for (const [key, value] of urlParams.entries()) {
      info.searchParams[key] = value
    }

    // Get hash params
    const hashParams = new URLSearchParams(window.location.hash.substring(1))
    for (const [key, value] of hashParams.entries()) {
      info.hashParams[key] = value
    }

    // Get Next.js searchParams
    info.nextSearchParams = {}
    searchParams.forEach((value, key) => {
      info.nextSearchParams[key] = value
    })

    setUrlInfo(info)
    console.log('URL Debug Info:', info)
  }, [searchParams])

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Email Confirmation Debug</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">URL Information</h2>
          <div className="space-y-2 font-mono text-sm">
            <div><strong>Full URL:</strong> {urlInfo.fullUrl}</div>
            <div><strong>Pathname:</strong> {urlInfo.pathname}</div>
            <div><strong>Search:</strong> {urlInfo.search}</div>
            <div><strong>Hash:</strong> {urlInfo.hash}</div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Search Parameters</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(urlInfo.searchParams, null, 2)}
          </pre>
        </div>

        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Hash Parameters</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(urlInfo.hashParams, null, 2)}
          </pre>
        </div>

        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Next.js Search Parameters</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(urlInfo.nextSearchParams, null, 2)}
          </pre>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-900 mb-2">Instructions:</h3>
          <p className="text-blue-800 text-sm">
            Click the email confirmation link and check this page to see what parameters are being sent.
            This will help us debug the email confirmation issue.
          </p>
        </div>
      </div>
    </div>
  )
}
