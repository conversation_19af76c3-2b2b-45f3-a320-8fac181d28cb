'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { BookOpen, Eye, EyeOff, Loader2, User, GraduationCap } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

export default function SignUpPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    role: '' as 'student' | 'teacher' | ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Validation
      if (formData.password !== formData.confirmPassword) {
        toast.error('Passwords do not match')
        setIsLoading(false)
        return
      }

      if (!formData.role) {
        toast.error('Please select your role')
        setIsLoading(false)
        return
      }

      if (!formData.fullName.trim()) {
        toast.error('Please enter your full name')
        setIsLoading(false)
        return
      }

      if (!formData.email.trim()) {
        toast.error('Please enter your email')
        setIsLoading(false)
        return
      }

      console.log('Starting signup process for:', formData.email)

      // Sign up user
      const { data, error } = await supabase.auth.signUp({
        email: formData.email.trim(),
        password: formData.password,
        options: {
          data: {
            full_name: formData.fullName.trim(),
            role: formData.role,
            email: formData.email.trim()
          },
          emailRedirectTo: `${window.location.origin}/auth/confirm`
        }
      })

      if (error) {
        console.error('❌ Signup error:', error)

        // Provide user-friendly error messages
        let errorMessage = error.message
        if (error.message.includes('email_address_invalid')) {
          errorMessage = 'Please enter a valid email address.'
        } else if (error.message.includes('password')) {
          errorMessage = 'Password must be at least 8 characters long.'
        } else if (error.message.includes('User already registered')) {
          errorMessage = 'An account with this email already exists. Try signing in instead.'
        } else if (error.message.includes('Database error')) {
          errorMessage = 'There was a technical issue. Please try again in a moment.'
        }

        toast.error('Sign Up Failed', {
          description: errorMessage,
          duration: 5000,
        })
        setIsLoading(false)
        return
      }

      if (data.user) {
        console.log('✅ User created successfully:', data.user.id)
        console.log('📧 Email confirmation required:', !data.user.email_confirmed_at)

        toast.success('Account Created Successfully!', {
          description: 'Please check your email to verify your account and complete signup.',
          duration: 5000,
        })

        // Redirect to email confirmation waiting page
        router.push('/auth/check-email')
      } else {
        console.error('❌ No user returned from signup')
        toast.error('Sign Up Failed', {
          description: 'Failed to create account. Please try again.',
        })
        setIsLoading(false)
      }
    } catch (error) {
      console.error('Sign up error:', error)
      toast.error('Sign Up Failed', {
        description: 'An unexpected error occurred. Please try again.',
      })
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-edubridge-50 via-white to-edubridge-100 p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2 mb-6">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-edubridge-500 to-edubridge-600">
              <BookOpen className="h-6 w-6 text-white" />
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-edubridge-600 to-edubridge-800 bg-clip-text text-transparent">
              EduBridge
            </span>
          </Link>
        </div>

        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Join EduBridge</CardTitle>
            <CardDescription>
              Create your account to start learning
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSignUp} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="fullName" className="text-sm font-medium">
                  Full Name
                </label>
                <Input
                  id="fullName"
                  type="text"
                  placeholder="Enter your full name"
                  value={formData.fullName}
                  onChange={(e) => handleInputChange('fullName', e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium">
                  Email
                </label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">I am a:</label>
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    type="button"
                    variant={formData.role === 'student' ? 'default' : 'outline'}
                    className="h-auto p-4 flex flex-col items-center space-y-2"
                    onClick={() => handleInputChange('role', 'student')}
                  >
                    <User className="h-6 w-6" />
                    <span>Student</span>
                  </Button>
                  <Button
                    type="button"
                    variant={formData.role === 'teacher' ? 'default' : 'outline'}
                    className="h-auto p-4 flex flex-col items-center space-y-2"
                    onClick={() => handleInputChange('role', 'teacher')}
                  >
                    <GraduationCap className="h-6 w-6" />
                    <span>Teacher</span>
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium">
                  Password
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Create a password"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="confirmPassword" className="text-sm font-medium">
                  Confirm Password
                </label>
                <Input
                  id="confirmPassword"
                  type="password"
                  placeholder="Confirm your password"
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  required
                />
              </div>

              <Button
                type="submit"
                className="w-full"
                variant="gradient"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Account...
                  </>
                ) : (
                  'Create Account'
                )}
              </Button>
            </form>

            <div className="mt-6 text-center text-sm text-muted-foreground">
              Already have an account?{' '}
              <Link
                href="/auth/signin"
                className="text-edubridge-600 hover:text-edubridge-700 hover:underline font-medium"
              >
                Sign in
              </Link>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
