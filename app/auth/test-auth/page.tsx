'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestAuthPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testSignUp = async () => {
    setLoading(true)
    setResult(null)

    try {
      const { data, error } = await supabase.auth.signUp({
        email: email,
        password: password,
        options: {
          data: {
            full_name: 'Test User',
            role: 'student',
            email: email
          },
          emailRedirectTo: `${window.location.origin}/auth/debug-confirm`
        }
      })

      setResult({
        type: 'signup',
        data,
        error,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      setResult({
        type: 'signup',
        error: error,
        timestamp: new Date().toISOString()
      })
    } finally {
      setLoading(false)
    }
  }

  const testSignIn = async () => {
    setLoading(true)
    setResult(null)

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email,
        password: password
      })

      setResult({
        type: 'signin',
        data,
        error,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      setResult({
        type: 'signin',
        error: error,
        timestamp: new Date().toISOString()
      })
    } finally {
      setLoading(false)
    }
  }

  const testSession = async () => {
    setLoading(true)
    setResult(null)

    try {
      const { data, error } = await supabase.auth.getSession()

      setResult({
        type: 'session',
        data,
        error,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      setResult({
        type: 'session',
        error: error,
        timestamp: new Date().toISOString()
      })
    } finally {
      setLoading(false)
    }
  }

  const testUser = async () => {
    setLoading(true)
    setResult(null)

    try {
      const { data, error } = await supabase.auth.getUser()

      setResult({
        type: 'user',
        data,
        error,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      setResult({
        type: 'user',
        error: error,
        timestamp: new Date().toISOString()
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Supabase Auth Test</h1>
        
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Credentials</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Email</label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Password</label>
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="password123"
              />
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button onClick={testSignUp} disabled={loading || !email || !password}>
                Test Sign Up
              </Button>
              <Button onClick={testSignIn} disabled={loading || !email || !password}>
                Test Sign In
              </Button>
              <Button onClick={testSession} disabled={loading}>
                Test Session
              </Button>
              <Button onClick={testUser} disabled={loading}>
                Test User
              </Button>
            </div>
          </CardContent>
        </Card>

        {result && (
          <Card>
            <CardHeader>
              <CardTitle>Result: {result.type}</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                {JSON.stringify(result, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-900 mb-2">Instructions:</h3>
          <ol className="text-blue-800 text-sm space-y-1">
            <li>1. Enter a test email and password</li>
            <li>2. Click "Test Sign Up" to test the signup flow</li>
            <li>3. Check your email for the confirmation link</li>
            <li>4. The confirmation link should redirect to /auth/debug-confirm</li>
            <li>5. Use other buttons to test different auth states</li>
          </ol>
        </div>
      </div>
    </div>
  )
}
