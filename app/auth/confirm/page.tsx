'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { CheckCircle, XCircle, Loader2 } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

export default function ConfirmPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'expired'>('loading')
  const [message, setMessage] = useState('')
  const [email, setEmail] = useState('')
  const [isResending, setIsResending] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()

  // Add timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (status === 'loading') {
        console.log('⏰ Confirmation timeout reached')
        setStatus('error')
        setMessage('Email confirmation is taking too long. Please try again.')
        toast.error('Timeout', {
          description: 'Please try clicking the email link again.'
        })
      }
    }, 15000) // 15 second timeout

    return () => clearTimeout(timeout)
  }, [status])

  const handleResendConfirmation = async () => {
    if (!email) {
      toast.error('Email Required', {
        description: 'Please enter your email address to resend confirmation.'
      })
      return
    }

    setIsResending(true)
    try {
      // First try to resend confirmation
      const { error: resendError } = await supabase.auth.resend({
        type: 'signup',
        email: email,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/confirm`
        }
      })

      if (resendError) {
        console.log('Resend failed, trying signup:', resendError.message)

        // If resend fails, the user might not exist, so try signing them up again
        const { error: signupError } = await supabase.auth.signUp({
          email: email,
          password: 'TempPassword123!', // Temporary password - user will reset it
          options: {
            emailRedirectTo: `${window.location.origin}/auth/confirm`,
            data: {
              role: 'student', // Default role
              full_name: ''
            }
          }
        })

        if (signupError && !signupError.message.includes('already registered')) {
          throw signupError
        }
      }

      toast.success('Email Sent!', {
        description: 'A new confirmation email has been sent. Please check your inbox and click the new link.'
      })

      // Clear the email field and show success message
      setEmail('')
      setMessage('A new confirmation email has been sent. Please check your inbox and click the new link.')

    } catch (error: any) {
      console.error('Resend exception:', error)
      toast.error('Resend Failed', {
        description: error.message || 'Failed to send confirmation email. Please try again.'
      })
    } finally {
      setIsResending(false)
    }
  }

  const handleSuccessfulAuth = async (user: any) => {
    try {
      console.log('🎉 Handling successful auth for user:', user.id)
      console.log('📋 User metadata:', user.user_metadata)

      // Get role from user metadata (set during signup)
      const role = user.user_metadata?.role || 'student'
      const fullName = user.user_metadata?.full_name || ''
      const email = user.email || user.user_metadata?.email || ''

      console.log('👤 User role:', role)

      // Check if profile already exists
      const { data: existingProfile, error: profileCheckError } = await supabase
        .from('profiles')
        .select('id, role, full_name, email')
        .eq('id', user.id)
        .maybeSingle()

      if (profileCheckError) {
        console.error('❌ Error checking profile:', profileCheckError)
        throw new Error('Failed to check user profile: ' + profileCheckError.message)
      }

      // Create profile if it doesn't exist
      if (!existingProfile) {
        console.log('🔨 Creating profile for user:', user.id)
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            id: user.id,
            email: email,
            full_name: fullName,
            role: role as 'student' | 'teacher' | 'admin'
          })

        if (profileError) {
          console.error('❌ Error creating profile:', profileError)
          throw new Error('Failed to create user profile: ' + profileError.message)
        }

        console.log('✅ Profile created successfully')
      } else {
        console.log('✅ Profile already exists:', existingProfile)
      }

      setStatus('success')
      setMessage('Email confirmed successfully! Redirecting to your dashboard...')

      toast.success('Welcome to EduBridge!', {
        description: `Your email has been confirmed successfully.`
      })

      // Redirect based on role after a short delay
      setTimeout(() => {
        console.log('🔄 Redirecting user with role:', role)
        if (role === 'admin') {
          router.push('/admin/dashboard')
        } else if (role === 'teacher') {
          router.push('/teacher/setup-profile')
        } else {
          router.push('/student/setup-profile')
        }
      }, 1500)
    } catch (error) {
      console.error('💥 Error handling successful auth:', error)
      setStatus('error')
      setMessage('Email confirmed but failed to set up your profile. Please try signing in manually.')
      toast.error('Profile Setup Failed', {
        description: 'Please try signing in manually or contact support.'
      })
    }
  }

  useEffect(() => {
    const handleEmailConfirmation = async () => {
      try {
        console.log('🚀 Starting email confirmation process...')
        console.log('📍 Current URL:', window.location.href)

        // Add a small delay to ensure the page is fully loaded
        await new Promise(resolve => setTimeout(resolve, 500))

        // First, check if user is already authenticated
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (session?.user) {
          console.log('✅ User already authenticated:', session.user.id)
          await handleSuccessfulAuth(session.user)
          return
        }

        // Get URL parameters from both search and hash
        const urlSearchParams = new URLSearchParams(window.location.search)
        const urlHashParams = new URLSearchParams(window.location.hash.substring(1))

        // Collect all possible parameters
        const params = {
          code: urlSearchParams.get('code') || urlHashParams.get('code'),
          access_token: urlSearchParams.get('access_token') || urlHashParams.get('access_token'),
          refresh_token: urlSearchParams.get('refresh_token') || urlHashParams.get('refresh_token'),
          token_hash: urlSearchParams.get('token_hash') || urlHashParams.get('token_hash'),
          type: urlSearchParams.get('type') || urlHashParams.get('type'),
          error: urlSearchParams.get('error') || urlHashParams.get('error'),
          error_code: urlSearchParams.get('error_code') || urlHashParams.get('error_code'),
          error_description: urlSearchParams.get('error_description') || urlHashParams.get('error_description'),
        }

        console.log('📋 URL Parameters found:', params)

        // Check for errors first
        if (params.error) {
          console.error('❌ Email confirmation error:', params.error)

          if (params.error_code === 'otp_expired' || params.error === 'access_denied') {
            setStatus('expired')
            setMessage('Your email confirmation link has expired. Please request a new one.')
            toast.error('Link Expired', {
              description: 'Please request a new confirmation email below.'
            })
          } else {
            setStatus('error')
            setMessage(params.error_description || params.error)
            toast.error('Confirmation Failed', {
              description: params.error_description || params.error
            })
          }
          return
        }

        let user = null

        // Method 1: PKCE Code Exchange (most common with newer Supabase)
        if (params.code) {
          console.log('🔄 Attempting PKCE code exchange...')
          const { data, error } = await supabase.auth.exchangeCodeForSession(params.code)
          if (error) {
            console.error('❌ PKCE failed:', error)
          } else if (data?.user) {
            user = data.user
            console.log('✅ PKCE success:', user.id)
          }
        }

        // Method 2: Direct session from hash parameters
        if (!user && params.access_token && params.refresh_token) {
          console.log('🔄 Attempting session from tokens...')
          const { data, error } = await supabase.auth.setSession({
            access_token: params.access_token,
            refresh_token: params.refresh_token
          })
          if (error) {
            console.error('❌ Session failed:', error)
          } else if (data?.user) {
            user = data.user
            console.log('✅ Session success:', user.id)
          }
        }

        // Method 3: OTP verification
        if (!user && params.token_hash && params.type) {
          console.log('🔄 Attempting OTP verification...')
          const { data, error } = await supabase.auth.verifyOtp({
            token_hash: params.token_hash,
            type: params.type as any
          })
          if (error) {
            console.error('❌ OTP failed:', error)
          } else if (data?.user) {
            user = data.user
            console.log('✅ OTP success:', user.id)
          }
        }

        // If no parameters found, try to get current session again
        if (!user && !params.code && !params.access_token && !params.token_hash) {
          console.log('🔄 No URL params found, checking session again...')
          const { data: { session: retrySession } } = await supabase.auth.getSession()
          if (retrySession?.user) {
            user = retrySession.user
            console.log('✅ Found session on retry:', user.id)
          }
        }

        if (user) {
          console.log('🎉 Authentication successful, setting up profile...')
          await handleSuccessfulAuth(user)
        } else {
          console.log('❌ No authentication method worked')
          setStatus('error')
          setMessage('Unable to confirm your email. The link may be invalid or expired.')
          toast.error('Confirmation Failed', {
            description: 'Please try signing in or request a new confirmation email.'
          })
        }
      } catch (error) {
        console.error('💥 Confirmation error:', error)
        setStatus('error')
        setMessage('An error occurred during email confirmation.')
        toast.error('Confirmation Failed', {
          description: 'Please try again or contact support.'
        })
      }
    }

    // Start the confirmation process immediately
    handleEmailConfirmation()
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-edubridge-50 to-white flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full"
      >
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          {status === 'loading' && (
            <>
              <Loader2 className="h-16 w-16 text-edubridge-600 animate-spin mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Confirming Your Email
              </h1>
              <p className="text-gray-600 mb-4">
                Please wait while we verify your email address...
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 text-sm text-edubridge-600 hover:text-edubridge-800 underline"
              >
                Taking too long? Click to retry
              </button>
            </>
          )}

          {status === 'success' && (
            <>
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Email Confirmed!
              </h1>
              <p className="text-gray-600 mb-4">
                {message}
              </p>
              <p className="text-sm text-gray-500">
                Redirecting you to complete your profile...
              </p>
            </>
          )}

          {status === 'error' && (
            <>
              <XCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Confirmation Failed
              </h1>
              <p className="text-gray-600 mb-6">
                {message}
              </p>
              <div className="space-y-3">
                <button
                  onClick={() => router.push('/auth/signin')}
                  className="w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors"
                >
                  Try Signing In
                </button>
                <button
                  onClick={() => router.push('/auth/signup')}
                  className="w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
                >
                  Sign Up Again
                </button>
              </div>
            </>
          )}

          {status === 'expired' && (
            <>
              <XCircle className="h-16 w-16 text-orange-600 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Link Expired
              </h1>
              <p className="text-gray-600 mb-6">
                {message}
              </p>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="resend-email" className="block text-sm font-medium text-gray-700">
                    Enter your email to resend confirmation:
                  </label>
                  <input
                    type="email"
                    id="resend-email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-edubridge-500"
                  />
                </div>
                <button
                  onClick={handleResendConfirmation}
                  disabled={isResending || !email}
                  className="w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isResending ? 'Sending...' : 'Resend Confirmation Email'}
                </button>
                <button
                  onClick={() => router.push('/auth/signup')}
                  className="w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
                >
                  Sign Up Again
                </button>
                <button
                  onClick={() => router.push('/auth/signin')}
                  className="w-full bg-blue-100 text-blue-700 py-2 px-4 rounded-md hover:bg-blue-200 transition-colors text-sm"
                >
                  Already have an account? Sign In
                </button>
              </div>
            </>
          )}
        </div>
      </motion.div>
    </div>
  )
}
