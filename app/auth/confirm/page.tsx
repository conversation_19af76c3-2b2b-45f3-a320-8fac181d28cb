'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { CheckCircle, XCircle, Loader2, BookOpen } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

export default function ConfirmPage() {
  const [status, setStatus] = useState<'success' | 'error'>('success')
  const [message, setMessage] = useState('')
  const router = useRouter()

  const handleSuccessfulAuth = async (user: any) => {
    try {
      console.log('🎉 Email confirmed for user:', user.id)

      // Get role from user metadata
      const role = user.user_metadata?.role || 'student'
      console.log('👤 User role:', role, 'metadata:', user.user_metadata)

      // IMMEDIATE redirect - no loading, no delays, no bullshit
      if (role === 'teacher') {
        console.log('🏫 Redirecting teacher to setup profile')
        router.replace('/teacher/setup-profile')
      } else {
        console.log('🎓 Redirecting student to setup profile')
        router.replace('/student/setup-profile')
      }

    } catch (error) {
      console.error('💥 Error handling successful auth:', error)
      setStatus('error')
      setMessage('Email confirmed but failed to redirect. Please try signing in.')
    }
  }

  // Listen for auth state changes
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔄 Auth state changed:', event, session?.user?.id)

      if (event === 'SIGNED_IN' && session?.user) {
        await handleSuccessfulAuth(session.user)
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  useEffect(() => {
    const handleEmailConfirmation = async () => {
      try {
        console.log('🚀 Starting email confirmation...')

        // NO DELAYS - check session immediately
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        console.log('📋 Session check result:', { session: !!session, user: session?.user?.id, error: sessionError })

        if (session?.user) {
          console.log('✅ User authenticated, redirecting immediately')
          await handleSuccessfulAuth(session.user)
          return
        }

        // If no session, wait a bit for auth state change
        console.log('⏳ No session found, waiting for auth state change...')
        setTimeout(() => {
          if (status === 'success') return // Already handled
          setStatus('error')
          setMessage('Email confirmation failed. Please try signing in manually.')
        }, 5000)

      } catch (error) {
        console.error('💥 Confirmation error:', error)
        setStatus('error')
        setMessage('An error occurred during email confirmation.')
      }
    }

    handleEmailConfirmation()
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-edubridge-50 to-white flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full"
      >
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          {/* Logo */}
          <div className="flex justify-center mb-6">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-edubridge-500 to-edubridge-600">
              <BookOpen className="h-7 w-7 text-white" />
            </div>
          </div>



          {status === 'success' && (
            <>
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Email Confirmed!
              </h1>
              <p className="text-gray-600 mb-4">
                Redirecting to your dashboard...
              </p>
            </>
          )}

          {status === 'error' && (
            <>
              <XCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Confirmation Failed
              </h1>
              <p className="text-gray-600 mb-6">
                {message}
              </p>
              <div className="space-y-3">
                <button
                  onClick={() => router.push('/auth/signin')}
                  className="w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors"
                >
                  Try Signing In
                </button>
                <button
                  onClick={() => router.push('/auth/signup')}
                  className="w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
                >
                  Sign Up Again
                </button>
              </div>
            </>
          )}
        </div>
      </motion.div>
    </div>
  )
}
