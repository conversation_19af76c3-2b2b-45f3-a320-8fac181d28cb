'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  BookOpen, 
  Play, 
  Clock, 
  Star, 
  Users,
  CheckCircle,
  BarChart3,
  Download,
  Calendar
} from 'lucide-react'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Navbar } from '@/components/layout/navbar'
import { useAuth } from '@/components/providers/auth-provider'

export default function StudentCoursesPage() {
  const { user, loading } = useAuth()
  const [activeTab, setActiveTab] = useState('enrolled')

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-edubridge-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Mock data for enrolled courses
  const enrolledCourses = [
    {
      id: '1',
      title: 'Complete Mathematics for Class 10',
      teacher: 'Dr. Ahmed Khan',
      teacherAvatar: '',
      subject: 'Mathematics',
      progress: 65,
      totalLessons: 25,
      completedLessons: 16,
      duration: '40 hours',
      rating: 4.8,
      enrolledAt: '2024-01-15',
      lastAccessed: '2024-01-20',
      nextLesson: 'Quadratic Equations - Part 2'
    },
    {
      id: '2',
      title: 'Physics Fundamentals',
      teacher: 'Prof. Fatima Ali',
      teacherAvatar: '',
      subject: 'Physics',
      progress: 40,
      totalLessons: 30,
      completedLessons: 12,
      duration: '35 hours',
      rating: 4.9,
      enrolledAt: '2024-01-10',
      lastAccessed: '2024-01-19',
      nextLesson: 'Laws of Motion'
    }
  ]

  // Mock data for available courses
  const availableCourses = [
    {
      id: '3',
      title: 'Chemistry for Class 11',
      teacher: 'Dr. Sarah Khan',
      teacherAvatar: '',
      subject: 'Chemistry',
      price: 3500,
      duration: '45 hours',
      lessons: 28,
      rating: 4.7,
      students: 450,
      description: 'Complete chemistry course covering all topics for Class 11 students.'
    },
    {
      id: '4',
      title: 'English Literature',
      teacher: 'Ms. Ayesha Ahmed',
      teacherAvatar: '',
      subject: 'English',
      price: 2800,
      duration: '30 hours',
      lessons: 20,
      rating: 4.6,
      students: 320,
      description: 'Comprehensive English literature course with poetry and prose analysis.'
    }
  ]

  const tabs = [
    { id: 'enrolled', label: 'My Courses', count: enrolledCourses.length },
    { id: 'available', label: 'Browse Courses', count: availableCourses.length },
    { id: 'completed', label: 'Completed', count: 0 }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold mb-2">My Learning</h1>
          <p className="text-muted-foreground">
            Track your progress and continue your educational journey
          </p>
        </motion.div>

        {/* Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-white text-edubridge-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {tab.label}
                {tab.count > 0 && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {tab.count}
                  </Badge>
                )}
              </button>
            ))}
          </div>
        </motion.div>

        {/* Enrolled Courses */}
        {activeTab === 'enrolled' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {enrolledCourses.map((course, index) => (
              <Card key={course.id} className="hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className="w-16 h-16 bg-gradient-to-br from-edubridge-500 to-edubridge-600 rounded-lg flex items-center justify-center">
                        <BookOpen className="h-8 w-8 text-white" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className="text-lg font-semibold mb-1">{course.title}</h3>
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={course.teacherAvatar} alt={course.teacher} />
                                <AvatarFallback className="text-xs">
                                  {course.teacher.split(' ').map(n => n[0]).join('')}
                                </AvatarFallback>
                              </Avatar>
                              <span>{course.teacher}</span>
                              <Badge variant="secondary" className="text-xs">
                                {course.subject}
                              </Badge>
                            </div>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="text-sm">{course.rating}</span>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div>
                            <div className="flex justify-between text-sm mb-1">
                              <span>Progress: {course.completedLessons}/{course.totalLessons} lessons</span>
                              <span>{course.progress}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-edubridge-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${course.progress}%` }}
                              ></div>
                            </div>
                          </div>

                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <div className="flex items-center space-x-1">
                              <Clock className="h-4 w-4" />
                              <span>{course.duration}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-4 w-4" />
                              <span>Last accessed: {new Date(course.lastAccessed).toLocaleDateString()}</span>
                            </div>
                          </div>

                          <div className="bg-blue-50 p-3 rounded-lg">
                            <p className="text-sm text-blue-800">
                              <strong>Next:</strong> {course.nextLesson}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col space-y-2 lg:w-48">
                      <Button className="w-full">
                        <Play className="mr-2 h-4 w-4" />
                        Continue Learning
                      </Button>
                      <Button variant="outline" size="sm" className="w-full">
                        <BarChart3 className="mr-2 h-4 w-4" />
                        View Progress
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {enrolledCourses.length === 0 && (
              <Card>
                <CardContent className="p-12 text-center">
                  <BookOpen className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No courses enrolled yet</h3>
                  <p className="text-muted-foreground mb-6">
                    Start your learning journey by enrolling in a course
                  </p>
                  <Button onClick={() => setActiveTab('available')}>
                    Browse Courses
                  </Button>
                </CardContent>
              </Card>
            )}
          </motion.div>
        )}

        {/* Available Courses */}
        {activeTab === 'available' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {availableCourses.map((course, index) => (
              <motion.div
                key={course.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <div className="w-full h-32 bg-gradient-to-br from-edubridge-500 to-edubridge-600 rounded-lg flex items-center justify-center mb-4">
                      <BookOpen className="h-12 w-12 text-white" />
                    </div>
                    <CardTitle className="text-lg">{course.title}</CardTitle>
                    <CardDescription>{course.description}</CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={course.teacherAvatar} alt={course.teacher} />
                        <AvatarFallback className="text-xs">
                          {course.teacher.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">{course.teacher}</p>
                        <Badge variant="secondary" className="text-xs">
                          {course.subject}
                        </Badge>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span>{course.rating}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Users className="h-4 w-4" />
                        <span>{course.students}</span>
                      </div>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>{course.duration}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <BookOpen className="h-4 w-4 text-muted-foreground" />
                        <span>{course.lessons} lessons</span>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <div className="text-center mb-4">
                        <span className="text-2xl font-bold text-edubridge-600">
                          PKR {course.price.toLocaleString()}
                        </span>
                      </div>
                      <Button className="w-full" variant="gradient">
                        Enroll Now
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Completed Courses */}
        {activeTab === 'completed' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card>
              <CardContent className="p-12 text-center">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No completed courses yet</h3>
                <p className="text-muted-foreground mb-6">
                  Complete your enrolled courses to see them here
                </p>
                <Button onClick={() => setActiveTab('enrolled')}>
                  View My Courses
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  )
}
