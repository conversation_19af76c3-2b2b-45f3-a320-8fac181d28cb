'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Video, 
  Calendar, 
  Clock, 
  Users,
  Play,
  Bell,
  BookOpen,
  Star,
  AlertCircle
} from 'lucide-react'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Navbar } from '@/components/layout/navbar'
import { useAuth } from '@/components/providers/auth-provider'

export default function StudentLiveClassesPage() {
  const { user, loading } = useAuth()
  const [activeTab, setActiveTab] = useState('upcoming')

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-edubridge-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Mock data for live classes
  const upcomingClasses = [
    {
      id: '1',
      title: 'Quadratic Equations - Problem Solving',
      teacher: 'Dr. Ahmed Khan',
      teacherAvatar: '',
      subject: 'Mathematics',
      date: '2024-01-22',
      time: '16:00',
      duration: 60,
      students: 25,
      maxStudents: 30,
      description: 'Interactive session on solving quadratic equations with real-world examples.',
      isSubscribed: true,
      classType: 'Premium'
    },
    {
      id: '2',
      title: 'Physics Lab - Laws of Motion',
      teacher: 'Prof. Fatima Ali',
      teacherAvatar: '',
      subject: 'Physics',
      date: '2024-01-23',
      time: '15:30',
      duration: 90,
      students: 18,
      maxStudents: 20,
      description: 'Virtual physics lab demonstrating Newton\'s laws with experiments.',
      isSubscribed: true,
      classType: 'Premium'
    },
    {
      id: '3',
      title: 'English Grammar Masterclass',
      teacher: 'Ms. Ayesha Ahmed',
      teacherAvatar: '',
      subject: 'English',
      date: '2024-01-24',
      time: '17:00',
      duration: 45,
      students: 35,
      maxStudents: 40,
      description: 'Comprehensive grammar session covering tenses and sentence structure.',
      isSubscribed: false,
      classType: 'Free'
    }
  ]

  const pastClasses = [
    {
      id: '4',
      title: 'Algebra Basics Review',
      teacher: 'Dr. Ahmed Khan',
      teacherAvatar: '',
      subject: 'Mathematics',
      date: '2024-01-20',
      time: '16:00',
      duration: 60,
      attended: true,
      recording: true,
      rating: 4.8
    },
    {
      id: '5',
      title: 'Chemistry Bonding',
      teacher: 'Dr. Sarah Khan',
      teacherAvatar: '',
      subject: 'Chemistry',
      date: '2024-01-19',
      time: '15:00',
      duration: 75,
      attended: false,
      recording: true,
      rating: 4.6
    }
  ]

  const tabs = [
    { id: 'upcoming', label: 'Upcoming', count: upcomingClasses.length },
    { id: 'past', label: 'Past Classes', count: pastClasses.length }
  ]

  const getTimeUntilClass = (date: string, time: string) => {
    const classDateTime = new Date(`${date}T${time}`)
    const now = new Date()
    const diff = classDateTime.getTime() - now.getTime()
    
    if (diff < 0) return 'Started'
    
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 24) {
      const days = Math.floor(hours / 24)
      return `${days} day${days > 1 ? 's' : ''}`
    }
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    
    return `${minutes}m`
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold mb-2">Live Classes</h1>
          <p className="text-muted-foreground">
            Join interactive live sessions with your teachers
          </p>
        </motion.div>

        {/* Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-white text-edubridge-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {tab.label}
                {tab.count > 0 && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {tab.count}
                  </Badge>
                )}
              </button>
            ))}
          </div>
        </motion.div>

        {/* Upcoming Classes */}
        {activeTab === 'upcoming' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {upcomingClasses.map((classItem, index) => (
              <Card key={classItem.id} className="hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-600 rounded-lg flex items-center justify-center">
                        <Video className="h-8 w-8 text-white" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <div className="flex items-center space-x-2 mb-1">
                              <h3 className="text-lg font-semibold">{classItem.title}</h3>
                              <Badge 
                                variant={classItem.classType === 'Free' ? 'success' : 'edubridge'}
                                className="text-xs"
                              >
                                {classItem.classType}
                              </Badge>
                            </div>
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={classItem.teacherAvatar} alt={classItem.teacher} />
                                <AvatarFallback className="text-xs">
                                  {classItem.teacher.split(' ').map(n => n[0]).join('')}
                                </AvatarFallback>
                              </Avatar>
                              <span>{classItem.teacher}</span>
                              <Badge variant="secondary" className="text-xs">
                                {classItem.subject}
                              </Badge>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium text-edubridge-600">
                              {getTimeUntilClass(classItem.date, classItem.time)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              until class
                            </div>
                          </div>
                        </div>

                        <p className="text-sm text-muted-foreground mb-3">
                          {classItem.description}
                        </p>

                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{new Date(classItem.date).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4" />
                            <span>{classItem.time} ({classItem.duration} min)</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Users className="h-4 w-4" />
                            <span>{classItem.students}/{classItem.maxStudents}</span>
                          </div>
                        </div>

                        {!classItem.isSubscribed && classItem.classType === 'Premium' && (
                          <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                            <div className="flex items-center space-x-2 text-orange-800">
                              <AlertCircle className="h-4 w-4" />
                              <span className="text-sm">
                                Subscribe to {classItem.teacher} to join this class
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col space-y-2 lg:w-48">
                      {classItem.isSubscribed || classItem.classType === 'Free' ? (
                        <>
                          <Button className="w-full">
                            <Play className="mr-2 h-4 w-4" />
                            Join Class
                          </Button>
                          <Button variant="outline" size="sm" className="w-full">
                            <Bell className="mr-2 h-4 w-4" />
                            Set Reminder
                          </Button>
                        </>
                      ) : (
                        <Button variant="outline" className="w-full" asChild>
                          <a href="/student/teachers">
                            Subscribe to Join
                          </a>
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {upcomingClasses.length === 0 && (
              <Card>
                <CardContent className="p-12 text-center">
                  <Video className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No upcoming classes</h3>
                  <p className="text-muted-foreground mb-6">
                    Subscribe to teachers to get access to their live classes
                  </p>
                  <Button asChild>
                    <a href="/student/teachers">
                      Find Teachers
                    </a>
                  </Button>
                </CardContent>
              </Card>
            )}
          </motion.div>
        )}

        {/* Past Classes */}
        {activeTab === 'past' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {pastClasses.map((classItem, index) => (
              <Card key={classItem.id} className="hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className={`w-16 h-16 rounded-lg flex items-center justify-center ${
                        classItem.attended 
                          ? 'bg-gradient-to-br from-green-500 to-green-600' 
                          : 'bg-gradient-to-br from-gray-400 to-gray-500'
                      }`}>
                        <Video className="h-8 w-8 text-white" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className="text-lg font-semibold mb-1">{classItem.title}</h3>
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={classItem.teacherAvatar} alt={classItem.teacher} />
                                <AvatarFallback className="text-xs">
                                  {classItem.teacher.split(' ').map(n => n[0]).join('')}
                                </AvatarFallback>
                              </Avatar>
                              <span>{classItem.teacher}</span>
                              <Badge variant="secondary" className="text-xs">
                                {classItem.subject}
                              </Badge>
                            </div>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="text-sm">{classItem.rating}</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-3">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{new Date(classItem.date).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4" />
                            <span>{classItem.time} ({classItem.duration} min)</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-4">
                          <Badge 
                            variant={classItem.attended ? 'success' : 'secondary'}
                            className="text-xs"
                          >
                            {classItem.attended ? 'Attended' : 'Missed'}
                          </Badge>
                          {classItem.recording && (
                            <Badge variant="outline" className="text-xs">
                              Recording Available
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col space-y-2 lg:w-48">
                      {classItem.recording && (
                        <Button variant="outline" className="w-full">
                          <Play className="mr-2 h-4 w-4" />
                          Watch Recording
                        </Button>
                      )}
                      {!classItem.attended && (
                        <Button variant="ghost" size="sm" className="w-full">
                          <BookOpen className="mr-2 h-4 w-4" />
                          View Notes
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {pastClasses.length === 0 && (
              <Card>
                <CardContent className="p-12 text-center">
                  <Video className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No past classes</h3>
                  <p className="text-muted-foreground">
                    Your attended classes will appear here
                  </p>
                </CardContent>
              </Card>
            )}
          </motion.div>
        )}
      </div>
    </div>
  )
}
