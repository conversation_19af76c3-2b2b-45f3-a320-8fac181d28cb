'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/components/providers/auth-provider'
import { Loader2, GraduationCap, BookOpen, Target, Clock, DollarSign, Brain, Users } from 'lucide-react'

interface Subject {
  id: string
  name: string
  category: string
  description: string
}

export default function StudentSetupProfilePage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [formData, setFormData] = useState({
    // Basic Info
    classLevel: '',
    board: '',
    gender: '',
    address: '',
    
    // Academic Info
    preferredSubjects: [] as string[],
    weakSubjects: [] as string[],
    studyGoals: '',
    learningStyle: '',
    
    // Parent/Guardian Info
    parentName: '',
    parentPhone: '',
    parentEmail: '',
    emergencyContact: '',
    emergencyPhone: '',
    
    // Learning Preferences
    preferredStudyTime: '',
    budgetRange: '',
    
    // Goals & Motivation
    learningGoals: '',
    targetGrades: ''
  })

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin')
    } else if (!loading && user && user.role !== 'student') {
      // Non-students don't need student profile setup
      switch (user.role) {
        case 'teacher':
          router.push('/teacher/setup-profile')
          break
        case 'admin':
          router.push('/admin/dashboard')
          break
        default:
          router.push('/student/dashboard')
      }
    }
  }, [user, loading, router])

  // Load subjects when component mounts
  useEffect(() => {
    const loadSubjects = async () => {
      const { data, error } = await supabase
        .from('subjects')
        .select('id, name, category, description')
        .eq('is_active', true)
        .order('category, name')

      if (error) {
        console.error('Error loading subjects:', error)
        // Create fallback subjects if table doesn't exist yet
        setSubjects([
          { id: '1', name: 'Mathematics', category: 'mathematics', description: 'Core mathematics' },
          { id: '2', name: 'Physics', category: 'physics', description: 'Physics concepts' },
          { id: '3', name: 'Chemistry', category: 'chemistry', description: 'Chemistry principles' },
          { id: '4', name: 'Biology', category: 'biology', description: 'Life sciences' },
          { id: '5', name: 'English', category: 'english', description: 'English language' },
          { id: '6', name: 'Urdu', category: 'urdu', description: 'Urdu language' },
        ])
      } else {
        setSubjects(data || [])
      }
    }

    loadSubjects()
  }, [])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubjectToggle = (subjectId: string, type: 'preferred' | 'weak') => {
    const field = type === 'preferred' ? 'preferredSubjects' : 'weakSubjects'
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(subjectId)
        ? prev[field].filter(id => id !== subjectId)
        : [...prev[field], subjectId]
    }))
  }

  const nextStep = () => {
    if (currentStep < 4) setCurrentStep(currentStep + 1)
  }

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1)
  }

  const getFilteredSubjects = () => {
    if (!formData.classLevel) return subjects
    const classLevel = parseInt(formData.classLevel)
    return subjects.filter(subject => 
      subject.name && (
        // For now, show all subjects. In production, filter by class_levels from subjects table
        true
      )
    )
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      if (!user) {
        toast.error('User not authenticated')
        setIsLoading(false)
        return
      }

      // Validation
      if (!formData.classLevel || !formData.board) {
        toast.error('Please fill in all required fields')
        setIsLoading(false)
        return
      }

      console.log('Creating student profile for user:', user.id)

      // First, create the basic profile record
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          email: user.email!,
          full_name: user.user_metadata?.full_name || '',
          role: 'student',
          phone: formData.parentPhone || null,
          city: formData.address || null
        })

      if (profileError && !profileError.message.includes('duplicate key')) {
        console.error('Profile creation error:', profileError)
        toast.error('Profile Creation Failed', {
          description: profileError.message
        })
        setIsLoading(false)
        return
      }

      // Create student profile
      const { error: studentError } = await supabase
        .from('students')
        .insert({
          profile_id: user.id,
          class_level: parseInt(formData.classLevel),
          board: formData.board as any,
          subjects: formData.preferredSubjects,
          parent_name: formData.parentName || null,
          parent_phone: formData.parentPhone || null,
          parent_email: formData.parentEmail || null,
          learning_goals: formData.learningGoals || null
        })

      if (studentError) {
        console.error('Student profile creation error:', studentError)
        toast.error('Student Profile Creation Failed', {
          description: studentError.message
        })
        setIsLoading(false)
        return
      }

      toast.success('Profile Setup Complete!', {
        description: 'Welcome to EduBridge! Redirecting to your dashboard...'
      })

      setTimeout(() => {
        router.push('/student/dashboard')
      }, 2000)

    } catch (error) {
      console.error('Profile setup error:', error)
      toast.error('An unexpected error occurred')
      setIsLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-edubridge-50 to-white flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-edubridge-600" />
      </div>
    )
  }

  if (!user || user.role !== 'student') {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-edubridge-50 to-white p-4">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="text-center mb-8">
            <GraduationCap className="h-16 w-16 text-edubridge-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900">Complete Your Student Profile</h1>
            <p className="text-gray-600 mt-2">
              Help us personalize your learning experience and find the perfect teachers for you
            </p>
          </div>

          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">Step {currentStep} of 4</span>
              <span className="text-sm text-gray-500">{Math.round((currentStep / 4) * 100)}% Complete</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-edubridge-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(currentStep / 4) * 100}%` }}
              />
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>
                {currentStep === 1 && "Basic Academic Information"}
                {currentStep === 2 && "Subject Preferences"}
                {currentStep === 3 && "Parent/Guardian Information"}
                {currentStep === 4 && "Learning Preferences"}
              </CardTitle>
              <CardDescription>
                {currentStep === 1 && "Tell us about your current academic level"}
                {currentStep === 2 && "Which subjects do you want to focus on?"}
                {currentStep === 3 && "Emergency contact and parent information"}
                {currentStep === 4 && "How do you prefer to learn?"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Step 1: Basic Academic Info */}
                {currentStep === 1 && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="classLevel">Current Class *</Label>
                        <Select value={formData.classLevel} onValueChange={(value) => handleInputChange('classLevel', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select your class" />
                          </SelectTrigger>
                          <SelectContent>
                            {[5, 6, 7, 8, 9, 10, 11, 12].map(level => (
                              <SelectItem key={level} value={level.toString()}>
                                Class {level}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="board">Education Board *</Label>
                        <Select value={formData.board} onValueChange={(value) => handleInputChange('board', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select your board" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="punjab">Punjab Board</SelectItem>
                            <SelectItem value="sindh">Sindh Board</SelectItem>
                            <SelectItem value="kpk">KPK Board</SelectItem>
                            <SelectItem value="federal">Federal Board</SelectItem>
                            <SelectItem value="balochistan">Balochistan Board</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="gender">Gender</Label>
                        <Select value={formData.gender} onValueChange={(value) => handleInputChange('gender', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select gender" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="male">Male</SelectItem>
                            <SelectItem value="female">Female</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="address">City/Address</Label>
                        <Input
                          id="address"
                          placeholder="e.g., Lahore, Karachi, Islamabad"
                          value={formData.address}
                          onChange={(e) => handleInputChange('address', e.target.value)}
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between pt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={prevStep}
                    disabled={currentStep === 1}
                  >
                    Previous
                  </Button>
                  
                  {currentStep < 4 ? (
                    <Button
                      type="button"
                      onClick={nextStep}
                      className="bg-edubridge-600 hover:bg-edubridge-700"
                      disabled={currentStep === 1 && (!formData.classLevel || !formData.board)}
                    >
                      Next
                    </Button>
                  ) : (
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="bg-edubridge-600 hover:bg-edubridge-700"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Setting up...
                        </>
                      ) : (
                        'Complete Setup'
                      )}
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
