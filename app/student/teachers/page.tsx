'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Search, 
  Filter, 
  Star, 
  Users, 
  BookOpen, 
  MapPin,
  Award,
  Clock
} from 'lucide-react'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Navbar } from '@/components/layout/navbar'
import { SubscriptionPlans } from '@/components/subscription/subscribe-button'
import { useAuth } from '@/components/providers/auth-provider'
import { toast } from 'sonner'

export default function TeachersPage() {
  const { user, loading } = useAuth()
  const [teachers, setTeachers] = useState([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedSubject, setSelectedSubject] = useState('')
  const [selectedTeacher, setSelectedTeacher] = useState<any>(null)

  useEffect(() => {
    loadTeachers()
  }, [])

  const loadTeachers = async () => {
    // Mock data for now - replace with real Supabase query
    const mockTeachers = [
      {
        id: '1',
        name: 'Dr. Ahmed Khan',
        avatar: '',
        subjects: ['Mathematics', 'Physics'],
        rating: 4.8,
        totalStudents: 1250,
        totalReviews: 89,
        experience: 8,
        qualifications: ['PhD Mathematics', 'MS Physics'],
        bio: 'Experienced mathematics and physics teacher specializing in Pakistani curriculum for classes 9-12. I focus on making complex concepts simple and engaging.',
        hourlyRate: 1500,
        city: 'Lahore',
        province: 'Punjab',
        classLevels: [9, 10, 11, 12],
        isVerified: true
      },
      {
        id: '2',
        name: 'Prof. Fatima Ali',
        avatar: '',
        subjects: ['Chemistry', 'Biology'],
        rating: 4.9,
        totalStudents: 890,
        totalReviews: 67,
        experience: 12,
        qualifications: ['PhD Chemistry', 'MS Biology'],
        bio: 'Passionate chemistry and biology educator with over 12 years of experience. I help students understand science through practical examples and real-world applications.',
        hourlyRate: 1800,
        city: 'Karachi',
        province: 'Sindh',
        classLevels: [9, 10, 11, 12],
        isVerified: true
      },
      {
        id: '3',
        name: 'Ustad Muhammad Hassan',
        avatar: '',
        subjects: ['Urdu', 'Islamiat', 'Pakistan Studies'],
        rating: 4.7,
        totalStudents: 650,
        totalReviews: 45,
        experience: 15,
        qualifications: ['MA Urdu', 'MA Islamic Studies'],
        bio: 'Dedicated teacher of Urdu, Islamiat, and Pakistan Studies. I believe in preserving our cultural heritage while making learning enjoyable for students.',
        hourlyRate: 1200,
        city: 'Islamabad',
        province: 'Islamabad',
        classLevels: [5, 6, 7, 8, 9, 10, 11, 12],
        isVerified: true
      }
    ]
    
    setTeachers(mockTeachers)
  }

  const subjects = [
    'All Subjects',
    'Mathematics',
    'Physics',
    'Chemistry',
    'Biology',
    'English',
    'Urdu',
    'Islamiat',
    'Pakistan Studies',
    'Computer Science'
  ]

  const filteredTeachers = teachers.filter(teacher => {
    const matchesSearch = teacher.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         teacher.subjects.some(subject => 
                           subject.toLowerCase().includes(searchQuery.toLowerCase())
                         )
    
    const matchesSubject = selectedSubject === '' || selectedSubject === 'All Subjects' ||
                          teacher.subjects.includes(selectedSubject)
    
    return matchesSearch && matchesSubject
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-edubridge-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading teachers...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold mb-2">Find Your Perfect Teacher</h1>
          <p className="text-muted-foreground">
            Connect with qualified teachers across Pakistan
          </p>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search teachers or subjects..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="md:w-48">
                  <select
                    value={selectedSubject}
                    onChange={(e) => setSelectedSubject(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    {subjects.map(subject => (
                      <option key={subject} value={subject}>
                        {subject}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Teachers Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
        >
          {filteredTeachers.map((teacher, index) => (
            <motion.div
              key={teacher.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <div className="flex items-start space-x-4">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={teacher.avatar} alt={teacher.name} />
                      <AvatarFallback>
                        {teacher.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <CardTitle className="text-lg">{teacher.name}</CardTitle>
                        {teacher.isVerified && (
                          <Badge variant="success" className="text-xs">
                            <Award className="w-3 h-3 mr-1" />
                            Verified
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span>{teacher.rating}</span>
                          <span>({teacher.totalReviews})</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Users className="h-4 w-4" />
                          <span>{teacher.totalStudents}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Subjects */}
                  <div>
                    <div className="flex flex-wrap gap-1">
                      {teacher.subjects.map(subject => (
                        <Badge key={subject} variant="secondary" className="text-xs">
                          {subject}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Bio */}
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {teacher.bio}
                  </p>

                  {/* Details */}
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>{teacher.experience} years experience</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span>{teacher.city}, {teacher.province}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <BookOpen className="h-4 w-4 text-muted-foreground" />
                      <span>Classes {teacher.classLevels.join(', ')}</span>
                    </div>
                  </div>

                  {/* Pricing */}
                  <div className="border-t pt-4">
                    <div className="text-center mb-4">
                      <span className="text-2xl font-bold text-edubridge-600">
                        PKR {teacher.hourlyRate}
                      </span>
                      <span className="text-sm text-muted-foreground">/hour</span>
                    </div>

                    <div className="space-y-2">
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => setSelectedTeacher(teacher)}
                      >
                        View Profile & Subscribe
                      </Button>
                      <Button variant="ghost" size="sm" className="w-full">
                        Send Message
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Teacher Profile Modal/Subscription */}
        {selectedTeacher && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            >
              <div className="p-6">
                <div className="flex justify-between items-start mb-6">
                  <div className="flex items-center space-x-4">
                    <Avatar className="h-20 w-20">
                      <AvatarImage src={selectedTeacher.avatar} alt={selectedTeacher.name} />
                      <AvatarFallback>
                        {selectedTeacher.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h2 className="text-2xl font-bold">{selectedTeacher.name}</h2>
                      <div className="flex items-center space-x-4 mt-1">
                        <div className="flex items-center space-x-1">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span>{selectedTeacher.rating}</span>
                        </div>
                        <span className="text-muted-foreground">
                          {selectedTeacher.totalStudents} students
                        </span>
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    onClick={() => setSelectedTeacher(null)}
                  >
                    ✕
                  </Button>
                </div>

                <div className="space-y-6">
                  <div>
                    <h3 className="font-semibold mb-2">About</h3>
                    <p className="text-muted-foreground">{selectedTeacher.bio}</p>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Qualifications</h3>
                    <div className="space-y-1">
                      {selectedTeacher.qualifications.map((qual, index) => (
                        <Badge key={index} variant="outline">
                          {qual}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-4">Subscription Plans</h3>
                    <SubscriptionPlans
                      teacherId={selectedTeacher.id}
                      onSubscriptionSuccess={(data) => {
                        toast.success('Subscription successful!')
                        setSelectedTeacher(null)
                      }}
                    />
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        )}

        {/* No Results */}
        {filteredTeachers.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No teachers found</h3>
            <p className="text-muted-foreground">
              Try adjusting your search criteria or browse all subjects
            </p>
          </motion.div>
        )}
      </div>
    </div>
  )
}
