'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  BookOpen,
  Users,
  GraduationCap,
  TrendingUp,
  Clock,
  Star,
  MessageCircle,
  Play,
  Award
} from 'lucide-react'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Navbar } from '@/components/layout/navbar'
import { SubscribeButton } from '@/components/subscription/subscribe-button'
import { useAuth } from '@/components/providers/auth-provider'
import { toast } from 'sonner'

export default function StudentDashboard() {
  const { user, loading } = useAuth()
  const [stats, setStats] = useState({
    coursesEnrolled: 0,
    teachersFollowed: 0,
    hoursLearned: 0,
    quizzesCompleted: 0
  })
  const [recentActivity, setRecentActivity] = useState([])
  const [recommendedTeachers, setRecommendedTeachers] = useState([])

  useEffect(() => {
    if (user) {
      loadDashboardData()
    }
  }, [user])

  const loadDashboardData = async () => {
    // Load mock data for now
    setStats({
      coursesEnrolled: 3,
      teachersFollowed: 5,
      hoursLearned: 24,
      quizzesCompleted: 12
    })

    setRecommendedTeachers([
      {
        id: '1',
        name: 'Dr. Ahmed Khan',
        subject: 'Mathematics',
        rating: 4.8,
        students: 1250,
        price: 2000
      },
      {
        id: '2',
        name: 'Prof. Fatima Ali',
        subject: 'Physics',
        rating: 4.9,
        students: 890,
        price: 2500
      }
    ])
  }

  const quickActions = [
    {
      title: 'AI Tutor',
      description: 'Get instant help with your studies',
      icon: GraduationCap,
      href: '/student/ai-tutor',
      color: 'bg-blue-500'
    },
    {
      title: 'Find Teachers',
      description: 'Browse qualified teachers',
      icon: Users,
      href: '/student/teachers',
      color: 'bg-green-500'
    },
    {
      title: 'My Courses',
      description: 'Continue your learning',
      icon: BookOpen,
      href: '/student/courses',
      color: 'bg-purple-500'
    },
    {
      title: 'Live Classes',
      description: 'Join upcoming sessions',
      icon: Play,
      href: '/student/live-classes',
      color: 'bg-red-500'
    }
  ]

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-edubridge-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-4">Please sign in to access your dashboard.</p>
          <Button asChild>
            <a href="/auth/signin">Sign In</a>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold mb-2">
            Welcome back, {user?.full_name || 'Student'}! 👋
          </h1>
          <p className="text-muted-foreground">
            Ready to continue your learning journey?
          </p>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Courses Enrolled</p>
                  <p className="text-2xl font-bold">{stats.coursesEnrolled}</p>
                </div>
                <BookOpen className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Teachers Followed</p>
                  <p className="text-2xl font-bold">{stats.teachersFollowed}</p>
                </div>
                <Users className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Hours Learned</p>
                  <p className="text-2xl font-bold">{stats.hoursLearned}</p>
                </div>
                <Clock className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Quizzes Completed</p>
                  <p className="text-2xl font-bold">{stats.quizzesCompleted}</p>
                </div>
                <Award className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Jump into your learning activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {quickActions.map((action, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      className="h-auto p-4 flex items-start space-x-3 hover:bg-gray-50"
                      asChild
                    >
                      <a href={action.href}>
                        <div className={`p-2 rounded-lg ${action.color}`}>
                          <action.icon className="h-5 w-5 text-white" />
                        </div>
                        <div className="text-left">
                          <p className="font-medium">{action.title}</p>
                          <p className="text-sm text-muted-foreground">
                            {action.description}
                          </p>
                        </div>
                      </a>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recommended Teachers */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Recommended Teachers</CardTitle>
                <CardDescription>
                  Top-rated teachers for you
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {recommendedTeachers.map((teacher) => (
                  <div key={teacher.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-medium">{teacher.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {teacher.subject}
                        </p>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm">{teacher.rating}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm text-muted-foreground">
                        {teacher.students} students
                      </span>
                      <Badge variant="secondary">
                        PKR {teacher.price}/month
                      </Badge>
                    </div>

                    <SubscribeButton
                      teacherId={teacher.id}
                      planType="monthly"
                      amount={teacher.price}
                      size="sm"
                      className="w-full"
                      onSubscriptionSuccess={(data) => {
                        toast.success('Subscribed successfully!')
                        loadDashboardData() // Refresh data
                      }}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-8"
        >
          <Card>
            <CardHeader>
              <CardTitle>Continue Learning</CardTitle>
              <CardDescription>
                Pick up where you left off
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Mock recent courses */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <BookOpen className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">Mathematics Class 10</h4>
                      <p className="text-sm text-muted-foreground">Dr. Ahmed Khan</p>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '65%' }}></div>
                  </div>
                  <p className="text-sm text-muted-foreground">65% complete</p>
                  <Button size="sm" className="w-full mt-3">
                    Continue Learning
                  </Button>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <GraduationCap className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">Physics Fundamentals</h4>
                      <p className="text-sm text-muted-foreground">Prof. Fatima Ali</p>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{ width: '40%' }}></div>
                  </div>
                  <p className="text-sm text-muted-foreground">40% complete</p>
                  <Button size="sm" className="w-full mt-3">
                    Continue Learning
                  </Button>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                      <MessageCircle className="h-6 w-6 text-purple-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">AI Tutor Session</h4>
                      <p className="text-sm text-muted-foreground">Chemistry Help</p>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    Last session: 2 hours ago
                  </p>
                  <Button size="sm" variant="outline" className="w-full">
                    <MessageCircle className="mr-2 h-4 w-4" />
                    Ask AI Tutor
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
