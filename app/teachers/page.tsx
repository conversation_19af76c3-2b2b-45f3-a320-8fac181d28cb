'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Search, 
  Star, 
  Users, 
  BookOpen, 
  MapPin,
  Award,
  Clock,
  GraduationCap
} from 'lucide-react'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Navbar } from '@/components/layout/navbar'

export default function PublicTeachersPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedSubject, setSelectedSubject] = useState('')

  // Mock teachers data
  const teachers = [
    {
      id: '1',
      name: 'Dr. <PERSON>',
      avatar: '',
      subjects: ['Mathematics', 'Physics'],
      rating: 4.8,
      totalStudents: 1250,
      totalReviews: 89,
      experience: 8,
      qualifications: ['PhD Mathematics', 'MS Physics'],
      bio: 'Experienced mathematics and physics teacher specializing in Pakistani curriculum for classes 9-12.',
      hourlyRate: 1500,
      city: 'Lahore',
      province: 'Punjab',
      classLevels: [9, 10, 11, 12],
      isVerified: true
    },
    {
      id: '2',
      name: 'Prof. Fatima Ali',
      avatar: '',
      subjects: ['Chemistry', 'Biology'],
      rating: 4.9,
      totalStudents: 890,
      totalReviews: 67,
      experience: 12,
      qualifications: ['PhD Chemistry', 'MS Biology'],
      bio: 'Passionate chemistry and biology educator with over 12 years of experience.',
      hourlyRate: 1800,
      city: 'Karachi',
      province: 'Sindh',
      classLevels: [9, 10, 11, 12],
      isVerified: true
    },
    {
      id: '3',
      name: 'Ustad Muhammad Hassan',
      avatar: '',
      subjects: ['Urdu', 'Islamiat', 'Pakistan Studies'],
      rating: 4.7,
      totalStudents: 650,
      totalReviews: 45,
      experience: 15,
      qualifications: ['MA Urdu', 'MA Islamic Studies'],
      bio: 'Dedicated teacher of Urdu, Islamiat, and Pakistan Studies.',
      hourlyRate: 1200,
      city: 'Islamabad',
      province: 'Islamabad',
      classLevels: [5, 6, 7, 8, 9, 10, 11, 12],
      isVerified: true
    }
  ]

  const subjects = [
    'All Subjects',
    'Mathematics',
    'Physics',
    'Chemistry',
    'Biology',
    'English',
    'Urdu',
    'Islamiat',
    'Pakistan Studies',
    'Computer Science'
  ]

  const filteredTeachers = teachers.filter(teacher => {
    const matchesSearch = teacher.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         teacher.subjects.some(subject => 
                           subject.toLowerCase().includes(searchQuery.toLowerCase())
                         )
    
    const matchesSubject = selectedSubject === '' || selectedSubject === 'All Subjects' ||
                          teacher.subjects.includes(selectedSubject)
    
    return matchesSearch && matchesSubject
  })

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Meet Our Expert Teachers
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Connect with qualified Pakistani teachers who understand your curriculum and learning needs.
          </p>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-12"
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search teachers or subjects..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="md:w-48">
                  <select
                    value={selectedSubject}
                    onChange={(e) => setSelectedSubject(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    {subjects.map(subject => (
                      <option key={subject} value={subject}>
                        {subject}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Teachers Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-16"
        >
          {filteredTeachers.map((teacher, index) => (
            <motion.div
              key={teacher.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <div className="flex items-start space-x-4">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={teacher.avatar} alt={teacher.name} />
                      <AvatarFallback>
                        {teacher.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <CardTitle className="text-lg">{teacher.name}</CardTitle>
                        {teacher.isVerified && (
                          <Badge variant="success" className="text-xs">
                            <Award className="w-3 h-3 mr-1" />
                            Verified
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span>{teacher.rating}</span>
                          <span>({teacher.totalReviews})</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Users className="h-4 w-4" />
                          <span>{teacher.totalStudents}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Subjects */}
                  <div>
                    <div className="flex flex-wrap gap-1">
                      {teacher.subjects.map(subject => (
                        <Badge key={subject} variant="secondary" className="text-xs">
                          {subject}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Bio */}
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {teacher.bio}
                  </p>

                  {/* Details */}
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>{teacher.experience} years experience</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span>{teacher.city}, {teacher.province}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <BookOpen className="h-4 w-4 text-muted-foreground" />
                      <span>Classes {teacher.classLevels.join(', ')}</span>
                    </div>
                  </div>

                  {/* Pricing */}
                  <div className="border-t pt-4">
                    <div className="text-center mb-4">
                      <span className="text-2xl font-bold text-edubridge-600">
                        PKR {teacher.hourlyRate}
                      </span>
                      <span className="text-sm text-muted-foreground">/hour</span>
                    </div>

                    <div className="space-y-2">
                      <Button variant="gradient" className="w-full" asChild>
                        <a href="/auth/signup">
                          Get Started - Sign Up
                        </a>
                      </Button>
                      <Button variant="ghost" size="sm" className="w-full">
                        View Profile
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="bg-gradient-to-br from-edubridge-500 to-edubridge-600 text-white">
            <CardContent className="p-12 text-center">
              <GraduationCap className="h-16 w-16 mx-auto mb-6 opacity-80" />
              <h2 className="text-3xl font-bold mb-4">
                Ready to Start Learning?
              </h2>
              <p className="text-xl mb-8 opacity-90">
                Join thousands of Pakistani students who are already learning with EduBridge
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" variant="secondary" asChild>
                  <a href="/auth/signup">
                    Sign Up as Student
                  </a>
                </Button>
                <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-edubridge-600" asChild>
                  <a href="/auth/signup">
                    Join as Teacher
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
