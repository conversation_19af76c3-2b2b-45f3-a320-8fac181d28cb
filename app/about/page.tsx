'use client'

import { motion } from 'framer-motion'
import { BookOpen, Users, GraduationCap, Award, Heart, Target } from 'lucide-react'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Navbar } from '@/components/layout/navbar'

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 }
}

export default function AboutPage() {
  const values = [
    {
      icon: BookOpen,
      title: 'Quality Education',
      description: 'We believe every Pakistani student deserves access to high-quality education that aligns with local curriculum standards.'
    },
    {
      icon: Users,
      title: 'Community First',
      description: 'Building a supportive community where students and teachers can connect, learn, and grow together.'
    },
    {
      icon: GraduationCap,
      title: 'Innovation in Learning',
      description: 'Leveraging AI and technology to make learning more engaging, personalized, and effective.'
    },
    {
      icon: Award,
      title: 'Excellence',
      description: 'Committed to maintaining the highest standards in education delivery and student outcomes.'
    }
  ]

  const stats = [
    { number: '10,000+', label: 'Active Students' },
    { number: '500+', label: 'Verified Teachers' },
    { number: '50,000+', label: 'Lessons Completed' },
    { number: '5', label: 'Education Boards Covered' }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <motion.div
          initial="initial"
          animate="animate"
          variants={fadeInUp}
          className="text-center mb-16"
        >
          <Badge variant="edubridge" className="mb-4">
            🇵🇰 Made for Pakistan
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-edubridge-600 to-edubridge-800 bg-clip-text text-transparent">
            Transforming Education in Pakistan
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            EduBridge is Pakistan's first AI-powered education platform, connecting students with qualified teachers 
            and providing personalized learning experiences aligned with Pakistani curriculum.
          </p>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial="initial"
          animate="animate"
          variants={fadeInUp}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16"
        >
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-edubridge-600 mb-2">
                {stat.number}
              </div>
              <div className="text-muted-foreground">{stat.label}</div>
            </div>
          ))}
        </motion.div>

        {/* Mission */}
        <motion.div
          initial="initial"
          animate="animate"
          variants={fadeInUp}
          className="mb-16"
        >
          <Card className="bg-gradient-to-br from-edubridge-50 to-white border-edubridge-200">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-edubridge-500 to-edubridge-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-8 w-8 text-white" />
              </div>
              <CardTitle className="text-2xl">Our Mission</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                To democratize quality education in Pakistan by leveraging technology and AI, 
                making learning accessible, affordable, and aligned with local curriculum standards 
                for every student across the country.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Values */}
        <motion.div
          initial="initial"
          animate="animate"
          variants={fadeInUp}
          className="mb-16"
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Values</h2>
            <p className="text-muted-foreground">
              The principles that guide everything we do
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <div className="w-12 h-12 bg-edubridge-100 rounded-lg flex items-center justify-center mb-4">
                    <value.icon className="h-6 w-6 text-edubridge-600" />
                  </div>
                  <CardTitle className="text-xl">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </motion.div>

        {/* Story */}
        <motion.div
          initial="initial"
          animate="animate"
          variants={fadeInUp}
          className="mb-16"
        >
          <Card>
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="h-8 w-8 text-white" />
              </div>
              <CardTitle className="text-2xl">Our Story</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-muted-foreground">
                EduBridge was born from a simple observation: millions of Pakistani students struggle to access 
                quality education due to geographical, financial, or resource constraints. Traditional tutoring 
                is expensive and often unavailable in remote areas.
              </p>
              
              <p className="text-muted-foreground">
                We envisioned a platform where any student, anywhere in Pakistan, could access world-class 
                education tailored to their local curriculum. By combining AI technology with human expertise, 
                we created a solution that's both affordable and effective.
              </p>

              <p className="text-muted-foreground">
                Today, EduBridge serves thousands of students across all provinces, helping them achieve their 
                academic goals while supporting teachers in reaching more students than ever before.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Features */}
        <motion.div
          initial="initial"
          animate="animate"
          variants={fadeInUp}
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose EduBridge?</h2>
            <p className="text-muted-foreground">
              Built specifically for Pakistani students and curriculum
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardHeader>
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <GraduationCap className="h-8 w-8 text-blue-600" />
                </div>
                <CardTitle>AI-Powered Learning</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Get personalized tutoring with our advanced AI that understands Pakistani curriculum 
                  and speaks both English and Urdu.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle>Verified Teachers</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Learn from qualified Pakistani teachers who understand local curriculum, 
                  culture, and student needs.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="h-8 w-8 text-purple-600" />
                </div>
                <CardTitle>Curriculum Aligned</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Content specifically designed for Punjab, Sindh, KPK, Federal, 
                  and Balochistan board curricula.
                </p>
              </CardContent>
            </Card>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
