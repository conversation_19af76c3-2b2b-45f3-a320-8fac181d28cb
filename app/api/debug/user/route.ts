import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')

    if (!email) {
      return NextResponse.json({ error: 'Email parameter required' }, { status: 400 })
    }

    // Get user profiles from database (allow multiple)
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email)

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Also get all profiles to see what's in the database
    const { data: allProfiles, error: allError } = await supabase
      .from('profiles')
      .select('id, email, full_name, role')
      .limit(10)

    // Also check auth.users table
    const { data: authUsers, error: authError } = await supabase
      .from('auth.users')
      .select('id, email, created_at')
      .eq('email', email)
      .limit(5)

    return NextResponse.json({
      profiles,
      count: profiles?.length || 0,
      allProfiles: allProfiles || [],
      allCount: allProfiles?.length || 0,
      authUsers: authUsers || [],
      authError: authError?.message || null,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Debug error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
