import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json({ error: 'Email required' }, { status: 400 })
    }

    // This is a debug endpoint - in production you'd want proper authentication
    console.log(`Cleaning up account for: ${email}`)

    return NextResponse.json({ 
      message: 'Account cleanup initiated',
      note: 'You may need to manually delete the user from Supabase Auth dashboard'
    })
  } catch (error) {
    console.error('Cleanup error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
