import { NextRequest, NextResponse } from 'next/server'
import { eduBridgeAI } from '@/lib/ai/openai'
import { createServerSupabase } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    // Get the current user (optional - can work without auth for demo)
    const supabase = createServerSupabase()
    const { data: { user } } = await supabase.auth.getUser()

    // Parse request body
    const body = await request.json()
    const { message, context, conversationHistory } = body

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      )
    }

    // Check if <PERSON>lla<PERSON> is running
    const status = await eduBridgeAI.checkOllamaStatus()
    if (!status.running) {
      return NextResponse.json({
        error: 'AI service is not available',
        details: status.error,
        suggestion: 'Please make sure <PERSON>lla<PERSON> is running with: ollama serve'
      }, { status: 503 })
    }

    if (!status.modelAvailable) {
      return NextResponse.json({
        error: 'AI model is not available',
        details: status.error,
        suggestion: 'Please download the model with: ollama pull llama3.1:8b'
      }, { status: 503 })
    }

    // Get AI response
    const response = await eduBridgeAI.chat(
      message,
      context,
      conversationHistory || []
    )

    return NextResponse.json({
      success: true,
      response,
      model: eduBridgeAI.getModelInfo(),
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('AI Chat API error:', error)
    
    return NextResponse.json({
      error: 'Failed to get AI response',
      details: error instanceof Error ? error.message : 'Unknown error',
      suggestion: 'Please check if Ollama is running and the model is available'
    }, { status: 500 })
  }
}
