import { NextResponse } from 'next/server'
import { eduBridgeAI } from '@/lib/ai/openai'

export async function GET() {
  try {
    const status = await eduBridgeAI.checkOllamaStatus()
    const modelInfo = eduBridgeAI.getModelInfo()

    return NextResponse.json({
      success: true,
      status: {
        ...status,
        ...modelInfo
      }
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      status: {
        running: false,
        modelAvailable: false,
        error: 'Failed to check Ollama status'
      }
    })
  }
}
