'use client'

import { useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import {
  BookOpen,
  Users,
  GraduationCap,
  Play,
  ArrowRight,
  Award,
  TrendingUp
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Navbar } from '@/components/layout/navbar'
import { useAuth } from '@/components/providers/auth-provider'

export default function HomePage() {
  const { user, loading } = useAuth()
  const router = useRouter()

  // Redirect authenticated users to their dashboard
  useEffect(() => {
    if (!loading && user) {
      // Also check if we're on /# (hash fragment) and redirect
      const isHashFragment = window.location.hash === '#' || window.location.pathname === '/'

      if (isHashFragment) {
        switch (user.role) {
          case 'admin':
            router.replace('/admin/dashboard')
            break
          case 'teacher':
            router.replace('/teacher/dashboard')
            break
          case 'student':
            router.replace('/student/dashboard')
            break
          default:
            // If no role is set, stay on homepage but clean URL
            if (window.location.hash === '#') {
              router.replace('/')
            }
            break
        }
      }
    }
  }, [user, loading, router])

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-edubridge-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // If user is authenticated, don't show the homepage content (they'll be redirected)
  if (user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-edubridge-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Redirecting to your dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-edubridge-50 via-white to-edubridge-100">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="container mx-auto px-4 py-20 lg:py-32">
          <div className="text-center max-w-4xl mx-auto">
            <div className="mb-6">
              <Badge variant="edubridge" className="mb-4">
                🇵🇰 Made for Pakistan
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-edubridge-600 via-edubridge-700 to-edubridge-800 bg-clip-text text-transparent mb-6">
                Transform Your Learning Journey with EduBridge
              </h1>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                Pakistan's first AI-powered education platform connecting students with qualified teachers.
                Learn with curriculum-aligned content, get AI tutoring, and join live classes.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button size="xl" variant="gradient" asChild>
                <Link href="/auth/signup">
                  Start Learning Free
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="xl" variant="outline" asChild>
                <Link href="/teachers">
                  <Play className="mr-2 h-5 w-5" />
                  Watch Demo
                </Link>
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-edubridge-600">10,000+</div>
                <div className="text-muted-foreground">Active Students</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-edubridge-600">500+</div>
                <div className="text-muted-foreground">Verified Teachers</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-edubridge-600">50,000+</div>
                <div className="text-muted-foreground">Lessons Completed</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Everything You Need to Excel in Your Studies
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              From AI-powered tutoring to live classes with expert teachers, we've got you covered.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: GraduationCap,
                title: "AI-Powered Learning",
                description: "Get personalized tutoring with our advanced AI that understands Pakistani curriculum and speaks both English and Urdu.",
                color: "text-blue-600"
              },
              {
                icon: Users,
                title: "Expert Teachers",
                description: "Learn from verified Pakistani teachers who understand local curriculum and cultural context.",
                color: "text-green-600"
              },
              {
                icon: BookOpen,
                title: "Curriculum Aligned",
                description: "Content specifically designed for Punjab, Sindh, KPK, Federal, and Balochistan boards.",
                color: "text-purple-600"
              },
              {
                icon: Play,
                title: "Live Classes",
                description: "Join interactive live sessions with teachers and fellow students for real-time learning.",
                color: "text-red-600"
              },
              {
                icon: Award,
                title: "Gamified Learning",
                description: "Earn points, badges, and compete with friends to make learning fun and engaging.",
                color: "text-yellow-600"
              },
              {
                icon: TrendingUp,
                title: "Progress Tracking",
                description: "Monitor your learning progress with detailed analytics and personalized recommendations.",
                color: "text-indigo-600"
              }
            ].map((feature, index) => (
              <div key={index}>
                <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <feature.icon className={`h-12 w-12 ${feature.color} mb-4`} />
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              How EduBridge Works
            </h2>
            <p className="text-xl text-muted-foreground">
              Get started in just 3 simple steps
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: "01",
                title: "Create Your Profile",
                description: "Sign up and tell us about your class, board, and subjects you want to learn."
              },
              {
                step: "02",
                title: "Choose Your Path",
                description: "Browse teachers, join live classes, or start learning with our AI tutor."
              },
              {
                step: "03",
                title: "Start Learning",
                description: "Begin your personalized learning journey and track your progress."
              }
            ].map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-edubridge-500 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-6">
                  {step.step}
                </div>
                <h3 className="text-xl font-semibold mb-4">{step.title}</h3>
                <p className="text-muted-foreground">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-edubridge-600 to-edubridge-700">
        <div className="container mx-auto px-4 text-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Transform Your Education?
            </h2>
            <p className="text-xl text-edubridge-100 mb-8 max-w-2xl mx-auto">
              Join thousands of Pakistani students who are already learning smarter with EduBridge.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="xl" variant="secondary" asChild>
                <Link href="/auth/signup">
                  Get Started Free
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="xl" variant="outline" className="text-white border-white hover:bg-white hover:text-edubridge-600" asChild>
                <Link href="/contact">
                  Contact Us
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <BookOpen className="h-6 w-6 text-edubridge-400" />
                <span className="text-xl font-bold">EduBridge</span>
              </div>
              <p className="text-gray-400">
                Empowering Pakistani students with AI-powered education and connecting them with qualified teachers.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Platform</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/students" className="hover:text-white">For Students</Link></li>
                <li><Link href="/teachers" className="hover:text-white">For Teachers</Link></li>
                <li><Link href="/ai-tutor" className="hover:text-white">AI Tutor</Link></li>
                <li><Link href="/live-classes" className="hover:text-white">Live Classes</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/help" className="hover:text-white">Help Center</Link></li>
                <li><Link href="/contact" className="hover:text-white">Contact Us</Link></li>
                <li><Link href="/privacy" className="hover:text-white">Privacy Policy</Link></li>
                <li><Link href="/terms" className="hover:text-white">Terms of Service</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Connect</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#" className="hover:text-white">Facebook</Link></li>
                <li><Link href="#" className="hover:text-white">Twitter</Link></li>
                <li><Link href="#" className="hover:text-white">Instagram</Link></li>
                <li><Link href="#" className="hover:text-white">LinkedIn</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 EduBridge. All rights reserved. Made with ❤️ for Pakistan.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
