/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/debug/user/route";
exports.ids = ["app/api/debug/user/route"];
exports.modules = {

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdebug%2Fuser%2Froute&page=%2Fapi%2Fdebug%2Fuser%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug%2Fuser%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdebug%2Fuser%2Froute&page=%2Fapi%2Fdebug%2Fuser%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug%2Fuser%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Edu_bridge_app_api_debug_user_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/debug/user/route.ts */ \"(rsc)/./app/api/debug/user/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/debug/user/route\",\n        pathname: \"/api/debug/user\",\n        filename: \"route\",\n        bundlePath: \"app/api/debug/user/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/api/debug/user/route.ts\",\n    nextConfigOutput,\n    userland: _Users_tahafarooqui_Documents_augment_projects_Edu_bridge_app_api_debug_user_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/debug/user/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdebug%2Fuser%2Froute&page=%2Fapi%2Fdebug%2Fuser%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug%2Fuser%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/debug/user/route.ts":
/*!*************************************!*\
  !*** ./app/api/debug/user/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./lib/supabase.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const email = searchParams.get(\"email\");\n        if (!email) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Email parameter required\"\n            }, {\n                status: 400\n            });\n        }\n        // Get user profiles from database (allow multiple)\n        const { data: profiles, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"profiles\").select(\"*\").eq(\"email\", email);\n        if (error) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: error.message\n            }, {\n                status: 500\n            });\n        }\n        // Also get all profiles to see what's in the database\n        const { data: allProfiles, error: allError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"profiles\").select(\"id, email, full_name, role\").limit(10);\n        // Also check auth.users table\n        const { data: authUsers, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"auth.users\").select(\"id, email, created_at\").eq(\"email\", email).limit(5);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            profiles,\n            count: profiles?.length || 0,\n            allProfiles: allProfiles || [],\n            allCount: allProfiles?.length || 0,\n            authUsers: authUsers || [],\n            authError: authError?.message || null,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Debug error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2RlYnVnL3VzZXIvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVEO0FBQ2Q7QUFFbEMsZUFBZUUsSUFBSUMsT0FBb0I7SUFDNUMsSUFBSTtRQUNGLE1BQU0sRUFBRUMsWUFBWSxFQUFFLEdBQUcsSUFBSUMsSUFBSUYsUUFBUUcsR0FBRztRQUM1QyxNQUFNQyxRQUFRSCxhQUFhSSxHQUFHLENBQUM7UUFFL0IsSUFBSSxDQUFDRCxPQUFPO1lBQ1YsT0FBT1Asa0ZBQVlBLENBQUNTLElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUEyQixHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDaEY7UUFFQSxtREFBbUQ7UUFDbkQsTUFBTSxFQUFFQyxNQUFNQyxRQUFRLEVBQUVILEtBQUssRUFBRSxHQUFHLE1BQU1ULG1EQUFRQSxDQUM3Q2EsSUFBSSxDQUFDLFlBQ0xDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsU0FBU1Q7UUFFZixJQUFJRyxPQUFPO1lBQ1QsT0FBT1Ysa0ZBQVlBLENBQUNTLElBQUksQ0FBQztnQkFBRUMsT0FBT0EsTUFBTU8sT0FBTztZQUFDLEdBQUc7Z0JBQUVOLFFBQVE7WUFBSTtRQUNuRTtRQUVBLHNEQUFzRDtRQUN0RCxNQUFNLEVBQUVDLE1BQU1NLFdBQVcsRUFBRVIsT0FBT1MsUUFBUSxFQUFFLEdBQUcsTUFBTWxCLG1EQUFRQSxDQUMxRGEsSUFBSSxDQUFDLFlBQ0xDLE1BQU0sQ0FBQyw4QkFDUEssS0FBSyxDQUFDO1FBRVQsOEJBQThCO1FBQzlCLE1BQU0sRUFBRVIsTUFBTVMsU0FBUyxFQUFFWCxPQUFPWSxTQUFTLEVBQUUsR0FBRyxNQUFNckIsbURBQVFBLENBQ3pEYSxJQUFJLENBQUMsY0FDTEMsTUFBTSxDQUFDLHlCQUNQQyxFQUFFLENBQUMsU0FBU1QsT0FDWmEsS0FBSyxDQUFDO1FBRVQsT0FBT3BCLGtGQUFZQSxDQUFDUyxJQUFJLENBQUM7WUFDdkJJO1lBQ0FVLE9BQU9WLFVBQVVXLFVBQVU7WUFDM0JOLGFBQWFBLGVBQWUsRUFBRTtZQUM5Qk8sVUFBVVAsYUFBYU0sVUFBVTtZQUNqQ0gsV0FBV0EsYUFBYSxFQUFFO1lBQzFCQyxXQUFXQSxXQUFXTCxXQUFXO1lBQ2pDUyxXQUFXLElBQUlDLE9BQU9DLFdBQVc7UUFDbkM7SUFDRixFQUFFLE9BQU9sQixPQUFPO1FBQ2RtQixRQUFRbkIsS0FBSyxDQUFDLGdCQUFnQkE7UUFDOUIsT0FBT1Ysa0ZBQVlBLENBQUNTLElBQUksQ0FBQztZQUFFQyxPQUFPO1FBQXdCLEdBQUc7WUFBRUMsUUFBUTtRQUFJO0lBQzdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lZHVicmlkZ2UvLi9hcHAvYXBpL2RlYnVnL3VzZXIvcm91dGUudHM/MDAxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInXG5pbXBvcnQgeyBzdXBhYmFzZSB9IGZyb20gJ0AvbGliL3N1cGFiYXNlJ1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBzZWFyY2hQYXJhbXMgfSA9IG5ldyBVUkwocmVxdWVzdC51cmwpXG4gICAgY29uc3QgZW1haWwgPSBzZWFyY2hQYXJhbXMuZ2V0KCdlbWFpbCcpXG5cbiAgICBpZiAoIWVtYWlsKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ0VtYWlsIHBhcmFtZXRlciByZXF1aXJlZCcgfSwgeyBzdGF0dXM6IDQwMCB9KVxuICAgIH1cblxuICAgIC8vIEdldCB1c2VyIHByb2ZpbGVzIGZyb20gZGF0YWJhc2UgKGFsbG93IG11bHRpcGxlKVxuICAgIGNvbnN0IHsgZGF0YTogcHJvZmlsZXMsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3Byb2ZpbGVzJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLmVxKCdlbWFpbCcsIGVtYWlsKVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogZXJyb3IubWVzc2FnZSB9LCB7IHN0YXR1czogNTAwIH0pXG4gICAgfVxuXG4gICAgLy8gQWxzbyBnZXQgYWxsIHByb2ZpbGVzIHRvIHNlZSB3aGF0J3MgaW4gdGhlIGRhdGFiYXNlXG4gICAgY29uc3QgeyBkYXRhOiBhbGxQcm9maWxlcywgZXJyb3I6IGFsbEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3Byb2ZpbGVzJylcbiAgICAgIC5zZWxlY3QoJ2lkLCBlbWFpbCwgZnVsbF9uYW1lLCByb2xlJylcbiAgICAgIC5saW1pdCgxMClcblxuICAgIC8vIEFsc28gY2hlY2sgYXV0aC51c2VycyB0YWJsZVxuICAgIGNvbnN0IHsgZGF0YTogYXV0aFVzZXJzLCBlcnJvcjogYXV0aEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2F1dGgudXNlcnMnKVxuICAgICAgLnNlbGVjdCgnaWQsIGVtYWlsLCBjcmVhdGVkX2F0JylcbiAgICAgIC5lcSgnZW1haWwnLCBlbWFpbClcbiAgICAgIC5saW1pdCg1KVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHByb2ZpbGVzLFxuICAgICAgY291bnQ6IHByb2ZpbGVzPy5sZW5ndGggfHwgMCxcbiAgICAgIGFsbFByb2ZpbGVzOiBhbGxQcm9maWxlcyB8fCBbXSxcbiAgICAgIGFsbENvdW50OiBhbGxQcm9maWxlcz8ubGVuZ3RoIHx8IDAsXG4gICAgICBhdXRoVXNlcnM6IGF1dGhVc2VycyB8fCBbXSxcbiAgICAgIGF1dGhFcnJvcjogYXV0aEVycm9yPy5tZXNzYWdlIHx8IG51bGwsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH0pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRGVidWcgZXJyb3I6JywgZXJyb3IpXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InIH0sIHsgc3RhdHVzOiA1MDAgfSlcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsInN1cGFiYXNlIiwiR0VUIiwicmVxdWVzdCIsInNlYXJjaFBhcmFtcyIsIlVSTCIsInVybCIsImVtYWlsIiwiZ2V0IiwianNvbiIsImVycm9yIiwic3RhdHVzIiwiZGF0YSIsInByb2ZpbGVzIiwiZnJvbSIsInNlbGVjdCIsImVxIiwibWVzc2FnZSIsImFsbFByb2ZpbGVzIiwiYWxsRXJyb3IiLCJsaW1pdCIsImF1dGhVc2VycyIsImF1dGhFcnJvciIsImNvdW50IiwibGVuZ3RoIiwiYWxsQ291bnQiLCJ0aW1lc3RhbXAiLCJEYXRlIiwidG9JU09TdHJpbmciLCJjb25zb2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/debug/user/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClientSupabase: () => (/* binding */ createClientSupabase),\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst supabaseUrl = \"https://cosmaeoxoprxbacgacwr.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNvc21hZW94b3ByeGJhY2dhY3dyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MDA2MTYsImV4cCI6MjA2NTA3NjYxNn0.SjhGBtyulwiLJKIatH87wqXMFTJ0vfvDDv-nTdw2eXo\";\n// Client-side Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        flowType: \"pkce\",\n        autoRefreshToken: true,\n        detectSessionInUrl: true,\n        persistSession: true\n    }\n});\n// Client component Supabase client\nconst createClientSupabase = ()=>(0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n// Server component Supabase client (only use in server components)\nconst createServerSupabase = ()=>{\n    const { cookies } = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createServerComponentClient)({\n        cookies\n    });\n};\n// Admin client for server-side operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, \"your_service_role_key_here\", {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdebug%2Fuser%2Froute&page=%2Fapi%2Fdebug%2Fuser%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug%2Fuser%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();