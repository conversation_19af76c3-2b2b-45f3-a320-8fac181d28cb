/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/confirm/page";
exports.ids = ["app/auth/confirm/page"];
exports.modules = {

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fconfirm%2Fpage&page=%2Fauth%2Fconfirm%2Fpage&appPaths=%2Fauth%2Fconfirm%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fconfirm%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fconfirm%2Fpage&page=%2Fauth%2Fconfirm%2Fpage&appPaths=%2Fauth%2Fconfirm%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fconfirm%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'confirm',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/auth/confirm/page.tsx */ \"(rsc)/./app/auth/confirm/page.tsx\")), \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/confirm/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/confirm/page\",\n        pathname: \"/auth/confirm\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fconfirm%2Fpage&page=%2Fauth%2Fconfirm%2Fpage&appPaths=%2Fauth%2Fconfirm%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fconfirm%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp%2Fauth%2Fconfirm%2Fpage.tsx&server=true!":
/*!********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp%2Fauth%2Fconfirm%2Fpage.tsx&server=true! ***!
  \********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/auth/confirm/page.tsx */ \"(ssr)/./app/auth/confirm/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZ0YWhhZmFyb29xdWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGRWR1LWJyaWRnZSUyRmFwcCUyRmF1dGglMkZjb25maXJtJTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWR1YnJpZGdlLz9kMGE3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9FZHUtYnJpZGdlL2FwcC9hdXRoL2NvbmZpcm0vcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp%2Fauth%2Fconfirm%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp%2Fglobals.css&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fcomponents%2Fauth%2Fredirect-handler.tsx&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fcomponents%2Fproviders%2Fauth-provider.tsx&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fcomponents%2Fproviders%2Ftoast-provider.tsx&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp%2Fglobals.css&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fcomponents%2Fauth%2Fredirect-handler.tsx&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fcomponents%2Fproviders%2Fauth-provider.tsx&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fcomponents%2Fproviders%2Ftoast-provider.tsx&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth/redirect-handler.tsx */ \"(ssr)/./components/auth/redirect-handler.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/auth-provider.tsx */ \"(ssr)/./components/providers/auth-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/toast-provider.tsx */ \"(ssr)/./components/providers/toast-provider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZ0YWhhZmFyb29xdWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGRWR1LWJyaWRnZSUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZ0YWhhZmFyb29xdWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGRWR1LWJyaWRnZSUyRmNvbXBvbmVudHMlMkZhdXRoJTJGcmVkaXJlY3QtaGFuZGxlci50c3gmbW9kdWxlcz0lMkZVc2VycyUyRnRhaGFmYXJvb3F1aSUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZFZHUtYnJpZGdlJTJGY29tcG9uZW50cyUyRnByb3ZpZGVycyUyRmF1dGgtcHJvdmlkZXIudHN4Jm1vZHVsZXM9JTJGVXNlcnMlMkZ0YWhhZmFyb29xdWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGRWR1LWJyaWRnZSUyRmNvbXBvbmVudHMlMkZwcm92aWRlcnMlMkZ0b2FzdC1wcm92aWRlci50c3gmbW9kdWxlcz0lMkZVc2VycyUyRnRhaGFmYXJvb3F1aSUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZFZHUtYnJpZGdlJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBbUk7QUFDbkksNExBQXFJO0FBQ3JJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWR1YnJpZGdlLz9jNmRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9FZHUtYnJpZGdlL2NvbXBvbmVudHMvYXV0aC9yZWRpcmVjdC1oYW5kbGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9FZHUtYnJpZGdlL2NvbXBvbmVudHMvcHJvdmlkZXJzL2F1dGgtcHJvdmlkZXIudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0VkdS1icmlkZ2UvY29tcG9uZW50cy9wcm92aWRlcnMvdG9hc3QtcHJvdmlkZXIudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp%2Fglobals.css&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fcomponents%2Fauth%2Fredirect-handler.tsx&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fcomponents%2Fproviders%2Fauth-provider.tsx&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fcomponents%2Fproviders%2Ftoast-provider.tsx&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/auth/confirm/page.tsx":
/*!***********************************!*\
  !*** ./app/auth/confirm/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConfirmPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction ConfirmPage() {\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"loading\");\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isResending, setIsResending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Add timeout to prevent infinite loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeout = setTimeout(()=>{\n            if (status === \"loading\") {\n                console.log(\"⏰ Confirmation timeout reached\");\n                setStatus(\"error\");\n                setMessage(\"Email confirmation is taking too long. Please try again.\");\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Timeout\", {\n                    description: \"Please try clicking the email link again.\"\n                });\n            }\n        }, 15000) // 15 second timeout\n        ;\n        return ()=>clearTimeout(timeout);\n    }, [\n        status\n    ]);\n    const handleResendConfirmation = async ()=>{\n        if (!email) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Email Required\", {\n                description: \"Please enter your email address to resend confirmation.\"\n            });\n            return;\n        }\n        setIsResending(true);\n        try {\n            // First try to resend confirmation\n            const { error: resendError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.resend({\n                type: \"signup\",\n                email: email,\n                options: {\n                    emailRedirectTo: `${window.location.origin}/auth/confirm`\n                }\n            });\n            if (resendError) {\n                console.log(\"Resend failed, trying signup:\", resendError.message);\n                // If resend fails, the user might not exist, so try signing them up again\n                const { error: signupError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signUp({\n                    email: email,\n                    password: \"TempPassword123!\",\n                    options: {\n                        emailRedirectTo: `${window.location.origin}/auth/confirm`,\n                        data: {\n                            role: \"student\",\n                            full_name: \"\"\n                        }\n                    }\n                });\n                if (signupError && !signupError.message.includes(\"already registered\")) {\n                    throw signupError;\n                }\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Email Sent!\", {\n                description: \"A new confirmation email has been sent. Please check your inbox and click the new link.\"\n            });\n            // Clear the email field and show success message\n            setEmail(\"\");\n            setMessage(\"A new confirmation email has been sent. Please check your inbox and click the new link.\");\n        } catch (error) {\n            console.error(\"Resend exception:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Resend Failed\", {\n                description: error.message || \"Failed to send confirmation email. Please try again.\"\n            });\n        } finally{\n            setIsResending(false);\n        }\n    };\n    const handleSuccessfulAuth = async (user)=>{\n        try {\n            console.log(\"\\uD83C\\uDF89 Handling successful auth for user:\", user.id);\n            console.log(\"\\uD83D\\uDCCB User metadata:\", user.user_metadata);\n            // Get role from user metadata (set during signup)\n            const role = user.user_metadata?.role || \"student\";\n            const fullName = user.user_metadata?.full_name || \"\";\n            const email = user.email || user.user_metadata?.email || \"\";\n            console.log(\"\\uD83D\\uDC64 User role:\", role);\n            // Check if profile already exists\n            const { data: existingProfile, error: profileCheckError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"id, role, full_name, email\").eq(\"id\", user.id).maybeSingle();\n            if (profileCheckError) {\n                console.error(\"❌ Error checking profile:\", profileCheckError);\n                throw new Error(\"Failed to check user profile: \" + profileCheckError.message);\n            }\n            // Create profile if it doesn't exist\n            if (!existingProfile) {\n                console.log(\"\\uD83D\\uDD28 Creating profile for user:\", user.id);\n                const { error: profileError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").insert({\n                    id: user.id,\n                    email: email,\n                    full_name: fullName,\n                    role: role\n                });\n                if (profileError) {\n                    console.error(\"❌ Error creating profile:\", profileError);\n                    throw new Error(\"Failed to create user profile: \" + profileError.message);\n                }\n                console.log(\"✅ Profile created successfully\");\n            } else {\n                console.log(\"✅ Profile already exists:\", existingProfile);\n            }\n            setStatus(\"success\");\n            setMessage(\"Email confirmed successfully! Redirecting to your dashboard...\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Welcome to EduBridge!\", {\n                description: `Your email has been confirmed successfully.`\n            });\n            // Redirect based on role after a short delay\n            setTimeout(()=>{\n                console.log(\"\\uD83D\\uDD04 Redirecting user with role:\", role);\n                if (role === \"admin\") {\n                    router.push(\"/admin/dashboard\");\n                } else if (role === \"teacher\") {\n                    router.push(\"/teacher/setup-profile\");\n                } else {\n                    router.push(\"/student/setup-profile\");\n                }\n            }, 1500);\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 Error handling successful auth:\", error);\n            setStatus(\"error\");\n            setMessage(\"Email confirmed but failed to set up your profile. Please try signing in manually.\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Profile Setup Failed\", {\n                description: \"Please try signing in manually or contact support.\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleEmailConfirmation = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDE80 Starting email confirmation process...\");\n                console.log(\"\\uD83D\\uDCCD Current URL:\", window.location.href);\n                // Add a small delay to ensure the page is fully loaded\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // First, check if user is already authenticated\n                const { data: { session }, error: sessionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                if (session?.user) {\n                    console.log(\"✅ User already authenticated:\", session.user.id);\n                    await handleSuccessfulAuth(session.user);\n                    return;\n                }\n                // Get URL parameters from both search and hash\n                const urlSearchParams = new URLSearchParams(window.location.search);\n                const urlHashParams = new URLSearchParams(window.location.hash.substring(1));\n                // Collect all possible parameters\n                const params = {\n                    code: urlSearchParams.get(\"code\") || urlHashParams.get(\"code\"),\n                    access_token: urlSearchParams.get(\"access_token\") || urlHashParams.get(\"access_token\"),\n                    refresh_token: urlSearchParams.get(\"refresh_token\") || urlHashParams.get(\"refresh_token\"),\n                    token_hash: urlSearchParams.get(\"token_hash\") || urlHashParams.get(\"token_hash\"),\n                    type: urlSearchParams.get(\"type\") || urlHashParams.get(\"type\"),\n                    error: urlSearchParams.get(\"error\") || urlHashParams.get(\"error\"),\n                    error_code: urlSearchParams.get(\"error_code\") || urlHashParams.get(\"error_code\"),\n                    error_description: urlSearchParams.get(\"error_description\") || urlHashParams.get(\"error_description\")\n                };\n                console.log(\"\\uD83D\\uDCCB URL Parameters found:\", params);\n                // Check for errors first\n                if (params.error) {\n                    console.error(\"❌ Email confirmation error:\", params.error);\n                    if (params.error_code === \"otp_expired\" || params.error === \"access_denied\") {\n                        setStatus(\"expired\");\n                        setMessage(\"Your email confirmation link has expired. Please request a new one.\");\n                        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Link Expired\", {\n                            description: \"Please request a new confirmation email below.\"\n                        });\n                    } else {\n                        setStatus(\"error\");\n                        setMessage(params.error_description || params.error);\n                        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                            description: params.error_description || params.error\n                        });\n                    }\n                    return;\n                }\n                let user = null;\n                // Method 1: PKCE Code Exchange (most common with newer Supabase)\n                if (params.code) {\n                    console.log(\"\\uD83D\\uDD04 Attempting PKCE code exchange...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.exchangeCodeForSession(params.code);\n                    if (error) {\n                        console.error(\"❌ PKCE failed:\", error);\n                    } else if (data?.user) {\n                        user = data.user;\n                        console.log(\"✅ PKCE success:\", user.id);\n                    }\n                }\n                // Method 2: Direct session from hash parameters\n                if (!user && params.access_token && params.refresh_token) {\n                    console.log(\"\\uD83D\\uDD04 Attempting session from tokens...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.setSession({\n                        access_token: params.access_token,\n                        refresh_token: params.refresh_token\n                    });\n                    if (error) {\n                        console.error(\"❌ Session failed:\", error);\n                    } else if (data?.user) {\n                        user = data.user;\n                        console.log(\"✅ Session success:\", user.id);\n                    }\n                }\n                // Method 3: OTP verification\n                if (!user && params.token_hash && params.type) {\n                    console.log(\"\\uD83D\\uDD04 Attempting OTP verification...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.verifyOtp({\n                        token_hash: params.token_hash,\n                        type: params.type\n                    });\n                    if (error) {\n                        console.error(\"❌ OTP failed:\", error);\n                    } else if (data?.user) {\n                        user = data.user;\n                        console.log(\"✅ OTP success:\", user.id);\n                    }\n                }\n                // If no parameters found, try to get current session again\n                if (!user && !params.code && !params.access_token && !params.token_hash) {\n                    console.log(\"\\uD83D\\uDD04 No URL params found, checking session again...\");\n                    const { data: { session: retrySession } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                    if (retrySession?.user) {\n                        user = retrySession.user;\n                        console.log(\"✅ Found session on retry:\", user.id);\n                    }\n                }\n                if (user) {\n                    console.log(\"\\uD83C\\uDF89 Authentication successful, setting up profile...\");\n                    await handleSuccessfulAuth(user);\n                } else {\n                    console.log(\"❌ No authentication method worked\");\n                    setStatus(\"error\");\n                    setMessage(\"Unable to confirm your email. The link may be invalid or expired.\");\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                        description: \"Please try signing in or request a new confirmation email.\"\n                    });\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDCA5 Confirmation error:\", error);\n                setStatus(\"error\");\n                setMessage(\"An error occurred during email confirmation.\");\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                    description: \"Please try again or contact support.\"\n                });\n            }\n        };\n        // Start the confirmation process immediately\n        handleEmailConfirmation();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-edubridge-50 to-white flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-md w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-8 text-center\",\n                children: [\n                    status === \"loading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-16 w-16 text-edubridge-600 animate-spin mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirming Your Email\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"Please wait while we verify your email address...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.reload(),\n                                className: \"px-4 py-2 text-sm text-edubridge-600 hover:text-edubridge-800 underline\",\n                                children: \"Taking too long? Click to retry\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-16 w-16 text-green-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Email Confirmed!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Redirecting you to complete your profile...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-red-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirmation Failed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signin\"),\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors\",\n                                        children: \"Try Signing In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"expired\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-orange-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Link Expired\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"resend-email\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Enter your email to resend confirmation:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                id: \"resend-email\",\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                placeholder: \"<EMAIL>\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-edubridge-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleResendConfirmation,\n                                        disabled: isResending || !email,\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isResending ? \"Sending...\" : \"Resend Confirmation Email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signin\"),\n                                        className: \"w-full bg-blue-100 text-blue-700 py-2 px-4 rounded-md hover:bg-blue-200 transition-colors text-sm\",\n                                        children: \"Already have an account? Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n            lineNumber: 303,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/auth/confirm/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/auth/redirect-handler.tsx":
/*!**********************************************!*\
  !*** ./components/auth/redirect-handler.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthRedirectHandler: () => (/* binding */ AuthRedirectHandler)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(ssr)/./components/providers/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ AuthRedirectHandler auto */ \n\n\nfunction AuthRedirectHandler() {\n    const { user, loading } = (0,_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Only run this effect in the browser\n        if (true) return;\n        const handleRedirect = async ()=>{\n            // Check if user landed on /# after email confirmation\n            if (window.location.hash === \"#\" && window.location.pathname === \"/\") {\n                if (!loading && user) {\n                    // User is authenticated and on /#, redirect to appropriate dashboard\n                    switch(user.role){\n                        case \"admin\":\n                            router.replace(\"/admin/dashboard\");\n                            break;\n                        case \"teacher\":\n                            router.replace(\"/teacher/dashboard\");\n                            break;\n                        case \"student\":\n                            router.replace(\"/student/dashboard\");\n                            break;\n                        default:\n                            // If no role, stay on homepage but remove the hash\n                            router.replace(\"/\");\n                            break;\n                    }\n                }\n            }\n        };\n        // Run immediately\n        handleRedirect();\n        // Also listen for hash changes\n        const handleHashChange = ()=>{\n            handleRedirect();\n        };\n        window.addEventListener(\"hashchange\", handleHashChange);\n        return ()=>{\n            window.removeEventListener(\"hashchange\", handleHashChange);\n        };\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    return null // This component doesn't render anything\n    ;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/auth/redirect-handler.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/auth-provider.tsx":
/*!************************************************!*\
  !*** ./components/providers/auth-provider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const loadUser = async ()=>{\n        try {\n            const { data: { user: authUser } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getUser();\n            if (authUser) {\n                // Get user profile to get additional data\n                const { data: profile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"role, full_name, avatar_url\").eq(\"id\", authUser.id).single();\n                setUser({\n                    ...authUser,\n                    role: profile?.role,\n                    full_name: profile?.full_name,\n                    avatar_url: profile?.avatar_url\n                });\n            } else {\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Error loading user:\", error);\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signOut();\n            if (error) {\n                console.error(\"Error signing out:\", error);\n            }\n            setUser(null);\n        } catch (error) {\n            console.error(\"Unexpected error during sign out:\", error);\n        }\n    };\n    const refreshUser = async ()=>{\n        await loadUser();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load initial user\n        loadUser();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth event:\", event, session?.user?.email);\n            if (event === \"SIGNED_IN\" || event === \"TOKEN_REFRESHED\") {\n                await loadUser();\n            // Don't auto-redirect on sign in - let the confirm page handle it\n            // This prevents conflicts with the email confirmation flow\n            } else if (event === \"SIGNED_OUT\") {\n                setUser(null);\n                setLoading(false);\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    const value = {\n        user,\n        loading,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/components/providers/auth-provider.tsx\",\n        lineNumber: 104,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/toast-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/toast-provider.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider auto */ \n\nfunction ToastProvider() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n        position: \"top-right\",\n        expand: false,\n        richColors: true,\n        closeButton: true,\n        toastOptions: {\n            style: {\n                background: \"hsl(var(--background))\",\n                color: \"hsl(var(--foreground))\",\n                border: \"1px solid hsl(var(--border))\"\n            }\n        }\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/components/providers/toast-provider.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy90b2FzdC1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFZ0M7QUFFekIsU0FBU0M7SUFDZCxxQkFDRSw4REFBQ0QsMkNBQU9BO1FBQ05FLFVBQVM7UUFDVEMsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZDLFdBQVc7UUFDWEMsY0FBYztZQUNaQyxPQUFPO2dCQUNMQyxZQUFZO2dCQUNaQyxPQUFPO2dCQUNQQyxRQUFRO1lBQ1Y7UUFDRjs7Ozs7O0FBR04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lZHVicmlkZ2UvLi9jb21wb25lbnRzL3Byb3ZpZGVycy90b2FzdC1wcm92aWRlci50c3g/YjQ3YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgVG9hc3RlciB9IGZyb20gJ3Nvbm5lcidcblxuZXhwb3J0IGZ1bmN0aW9uIFRvYXN0UHJvdmlkZXIoKSB7XG4gIHJldHVybiAoXG4gICAgPFRvYXN0ZXJcbiAgICAgIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCJcbiAgICAgIGV4cGFuZD17ZmFsc2V9XG4gICAgICByaWNoQ29sb3JzXG4gICAgICBjbG9zZUJ1dHRvblxuICAgICAgdG9hc3RPcHRpb25zPXt7XG4gICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgYmFja2dyb3VuZDogJ2hzbCh2YXIoLS1iYWNrZ3JvdW5kKSknLFxuICAgICAgICAgIGNvbG9yOiAnaHNsKHZhcigtLWZvcmVncm91bmQpKScsXG4gICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIGhzbCh2YXIoLS1ib3JkZXIpKScsXG4gICAgICAgIH0sXG4gICAgICB9fVxuICAgIC8+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJUb2FzdGVyIiwiVG9hc3RQcm92aWRlciIsInBvc2l0aW9uIiwiZXhwYW5kIiwicmljaENvbG9ycyIsImNsb3NlQnV0dG9uIiwidG9hc3RPcHRpb25zIiwic3R5bGUiLCJiYWNrZ3JvdW5kIiwiY29sb3IiLCJib3JkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/toast-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClientSupabase: () => (/* binding */ createClientSupabase),\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst supabaseUrl = \"https://cosmaeoxoprxbacgacwr.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNvc21hZW94b3ByeGJhY2dhY3dyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MDA2MTYsImV4cCI6MjA2NTA3NjYxNn0.SjhGBtyulwiLJKIatH87wqXMFTJ0vfvDDv-nTdw2eXo\";\n// Client-side Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        flowType: \"pkce\",\n        autoRefreshToken: true,\n        detectSessionInUrl: true,\n        persistSession: true\n    }\n});\n// Client component Supabase client\nconst createClientSupabase = ()=>(0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n// Server component Supabase client (only use in server components)\nconst createServerSupabase = ()=>{\n    const { cookies } = __webpack_require__(/*! next/headers */ \"(ssr)/./node_modules/next/headers.js\");\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createServerComponentClient)({\n        cookies\n    });\n};\n// Admin client for server-side operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, \"your_service_role_key_here\", {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"239c9a226aae\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lZHVicmlkZ2UvLi9hcHAvZ2xvYmFscy5jc3M/ODZjZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjIzOWM5YTIyNmFhZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/auth/confirm/page.tsx":
/*!***********************************!*\
  !*** ./app/auth/confirm/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_providers_toast_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/toast-provider */ \"(rsc)/./components/providers/toast-provider.tsx\");\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(rsc)/./components/providers/auth-provider.tsx\");\n/* harmony import */ var _components_auth_redirect_handler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth/redirect-handler */ \"(rsc)/./components/auth/redirect-handler.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"EduBridge - Online Learning & Teacher Marketplace for Pakistan\",\n    description: \"AI-powered education platform connecting Pakistani students with qualified teachers. Learn with curriculum-aligned content, AI tutoring, and live classes.\",\n    keywords: \"Pakistan education, online learning, AI tutor, Pakistani curriculum, teachers marketplace\",\n    authors: [\n        {\n            name: \"EduBridge Team\"\n        }\n    ],\n    creator: \"EduBridge\",\n    publisher: \"EduBridge\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"http://localhost:3000\" || 0),\n    openGraph: {\n        title: \"EduBridge - Online Learning & Teacher Marketplace for Pakistan\",\n        description: \"AI-powered education platform connecting Pakistani students with qualified teachers.\",\n        url: \"/\",\n        siteName: \"EduBridge\",\n        images: [\n            {\n                url: \"/og-image.png\",\n                width: 1200,\n                height: 630,\n                alt: \"EduBridge - Online Learning Platform\"\n            }\n        ],\n        locale: \"en_PK\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"EduBridge - Online Learning & Teacher Marketplace for Pakistan\",\n        description: \"AI-powered education platform connecting Pakistani students with qualified teachers.\",\n        images: [\n            \"/og-image.png\"\n        ],\n        creator: \"@edubridge_pk\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_redirect_handler__WEBPACK_IMPORTED_MODULE_4__.AuthRedirectHandler, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/layout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex min-h-screen flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/layout.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_toast_provider__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/layout.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/layout.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/layout.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/layout.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/auth/redirect-handler.tsx":
/*!**********************************************!*\
  !*** ./components/auth/redirect-handler.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthRedirectHandler: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/Edu-bridge/components/auth/redirect-handler.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/Edu-bridge/components/auth/redirect-handler.tsx#AuthRedirectHandler`);


/***/ }),

/***/ "(rsc)/./components/providers/auth-provider.tsx":
/*!************************************************!*\
  !*** ./components/providers/auth-provider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/Edu-bridge/components/providers/auth-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/Edu-bridge/components/providers/auth-provider.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/Edu-bridge/components/providers/auth-provider.tsx#useAuth`);


/***/ }),

/***/ "(rsc)/./components/providers/toast-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/toast-provider.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/Edu-bridge/components/providers/toast-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/Edu-bridge/components/providers/toast-provider.tsx#ToastProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/framer-motion","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/sonner","vendor-chunks/whatwg-url","vendor-chunks/lucide-react","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fconfirm%2Fpage&page=%2Fauth%2Fconfirm%2Fpage&appPaths=%2Fauth%2Fconfirm%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fconfirm%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();