"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signup/page",{

/***/ "(app-pages-browser)/./app/auth/signup/page.tsx":
/*!**********************************!*\
  !*** ./app/auth/signup/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SignUpPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_GraduationCap_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,GraduationCap,Loader2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_GraduationCap_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,GraduationCap,Loader2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_GraduationCap_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,GraduationCap,Loader2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_GraduationCap_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,GraduationCap,Loader2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_GraduationCap_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,GraduationCap,Loader2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_GraduationCap_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,GraduationCap,Loader2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SignUpPage() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        fullName: \"\",\n        role: \"\"\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSignUp = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            // Validation\n            if (formData.password !== formData.confirmPassword) {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Passwords do not match\");\n                setIsLoading(false);\n                return;\n            }\n            if (!formData.role) {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Please select your role\");\n                setIsLoading(false);\n                return;\n            }\n            if (!formData.fullName.trim()) {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Please enter your full name\");\n                setIsLoading(false);\n                return;\n            }\n            if (!formData.email.trim()) {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Please enter your email\");\n                setIsLoading(false);\n                return;\n            }\n            console.log(\"Starting signup process for:\", formData.email);\n            // Sign up user\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.auth.signUp({\n                email: formData.email.trim(),\n                password: formData.password,\n                options: {\n                    data: {\n                        full_name: formData.fullName.trim(),\n                        role: formData.role,\n                        email: formData.email.trim()\n                    },\n                    emailRedirectTo: \"\".concat(window.location.origin, \"/auth/confirm\")\n                }\n            });\n            if (error) {\n                console.error(\"❌ Signup error:\", error);\n                // Provide user-friendly error messages\n                let errorMessage = error.message;\n                if (error.message.includes(\"email_address_invalid\")) {\n                    errorMessage = \"Please enter a valid email address.\";\n                } else if (error.message.includes(\"password\")) {\n                    errorMessage = \"Password must be at least 8 characters long.\";\n                } else if (error.message.includes(\"User already registered\")) {\n                    errorMessage = \"An account with this email already exists. Try signing in instead.\";\n                } else if (error.message.includes(\"Database error\")) {\n                    errorMessage = \"There was a technical issue. Please try again in a moment.\";\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Sign Up Failed\", {\n                    description: errorMessage,\n                    duration: 5000\n                });\n                setIsLoading(false);\n                return;\n            }\n            if (data.user) {\n                console.log(\"✅ User created successfully:\", data.user.id);\n                console.log(\"\\uD83D\\uDCE7 Email confirmation required:\", !data.user.email_confirmed_at);\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Account Created Successfully!\", {\n                    description: \"Please check your email to verify your account and complete signup.\",\n                    duration: 5000\n                });\n                // Redirect to email confirmation waiting page\n                router.push(\"/auth/check-email\");\n            } else {\n                console.error(\"❌ No user returned from signup\");\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Sign Up Failed\", {\n                    description: \"Failed to create account. Please try again.\"\n                });\n                setIsLoading(false);\n            }\n        } catch (error) {\n            console.error(\"Sign up error:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Sign Up Failed\", {\n                description: \"An unexpected error occurred. Please try again.\"\n            });\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-edubridge-50 via-white to-edubridge-100 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"inline-flex items-center space-x-2 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-edubridge-500 to-edubridge-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_GraduationCap_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-6 w-6 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl font-bold bg-gradient-to-r from-edubridge-600 to-edubridge-800 bg-clip-text text-transparent\",\n                                children: \"EduBridge\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-2xl\",\n                                    children: \"Join EduBridge\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                    children: \"Create your account to start learning\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSignUp,\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"fullName\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Full Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"fullName\",\n                                                    type: \"text\",\n                                                    placeholder: \"Enter your full name\",\n                                                    value: formData.fullName,\n                                                    onChange: (e)=>handleInputChange(\"fullName\", e.target.value),\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"email\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"email\",\n                                                    type: \"email\",\n                                                    placeholder: \"Enter your email\",\n                                                    value: formData.email,\n                                                    onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"I am a:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: formData.role === \"student\" ? \"default\" : \"outline\",\n                                                            className: \"h-auto p-4 flex flex-col items-center space-y-2\",\n                                                            onClick: ()=>handleInputChange(\"role\", \"student\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_GraduationCap_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Student\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: formData.role === \"teacher\" ? \"default\" : \"outline\",\n                                                            className: \"h-auto p-4 flex flex-col items-center space-y-2\",\n                                                            onClick: ()=>handleInputChange(\"role\", \"teacher\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_GraduationCap_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Teacher\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"password\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"password\",\n                                                            type: showPassword ? \"text\" : \"password\",\n                                                            placeholder: \"Create a password\",\n                                                            value: formData.password,\n                                                            onChange: (e)=>handleInputChange(\"password\", e.target.value),\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_GraduationCap_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_GraduationCap_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"confirmPassword\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Confirm Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"confirmPassword\",\n                                                    type: \"password\",\n                                                    placeholder: \"Confirm your password\",\n                                                    value: formData.confirmPassword,\n                                                    onChange: (e)=>handleInputChange(\"confirmPassword\", e.target.value),\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full\",\n                                            variant: \"gradient\",\n                                            disabled: isLoading,\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_GraduationCap_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Creating Account...\"\n                                                ]\n                                            }, void 0, true) : \"Create Account\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center text-sm text-muted-foreground\",\n                                    children: [\n                                        \"Already have an account?\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/signin\",\n                                            className: \"text-edubridge-600 hover:text-edubridge-700 hover:underline font-medium\",\n                                            children: \"Sign in\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signup/page.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n_s(SignUpPage, \"700+VTpA24WkzeKR7AD7aSm1edg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SignUpPage;\nvar _c;\n$RefreshReg$(_c, \"SignUpPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/auth/signup/page.tsx\n"));

/***/ })

});