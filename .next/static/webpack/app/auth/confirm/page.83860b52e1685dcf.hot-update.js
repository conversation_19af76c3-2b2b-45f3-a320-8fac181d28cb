"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/confirm/page",{

/***/ "(app-pages-browser)/./app/auth/confirm/page.tsx":
/*!***********************************!*\
  !*** ./app/auth/confirm/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ConfirmPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ConfirmPage() {\n    _s();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"loading\");\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isResending, setIsResending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Add timeout to prevent infinite loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeout = setTimeout(()=>{\n            if (status === \"loading\") {\n                console.log(\"⏰ Confirmation timeout reached\");\n                setStatus(\"error\");\n                setMessage(\"Email confirmation is taking too long. Please try again.\");\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Timeout\", {\n                    description: \"Please try clicking the email link again.\"\n                });\n            }\n        }, 15000) // 15 second timeout\n        ;\n        return ()=>clearTimeout(timeout);\n    }, [\n        status\n    ]);\n    const handleResendConfirmation = async ()=>{\n        if (!email) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Email Required\", {\n                description: \"Please enter your email address to resend confirmation.\"\n            });\n            return;\n        }\n        setIsResending(true);\n        try {\n            // First try to resend confirmation\n            const { error: resendError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.resend({\n                type: \"signup\",\n                email: email,\n                options: {\n                    emailRedirectTo: \"\".concat(window.location.origin, \"/auth/confirm\")\n                }\n            });\n            if (resendError) {\n                console.log(\"Resend failed, trying signup:\", resendError.message);\n                // If resend fails, the user might not exist, so try signing them up again\n                const { error: signupError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signUp({\n                    email: email,\n                    password: \"TempPassword123!\",\n                    options: {\n                        emailRedirectTo: \"\".concat(window.location.origin, \"/auth/confirm\"),\n                        data: {\n                            role: \"student\",\n                            full_name: \"\"\n                        }\n                    }\n                });\n                if (signupError && !signupError.message.includes(\"already registered\")) {\n                    throw signupError;\n                }\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Email Sent!\", {\n                description: \"A new confirmation email has been sent. Please check your inbox and click the new link.\"\n            });\n            // Clear the email field and show success message\n            setEmail(\"\");\n            setMessage(\"A new confirmation email has been sent. Please check your inbox and click the new link.\");\n        } catch (error) {\n            console.error(\"Resend exception:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Resend Failed\", {\n                description: error.message || \"Failed to send confirmation email. Please try again.\"\n            });\n        } finally{\n            setIsResending(false);\n        }\n    };\n    const handleSuccessfulAuth = async (user)=>{\n        try {\n            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n            console.log(\"\\uD83C\\uDF89 Handling successful auth for user:\", user.id);\n            console.log(\"\\uD83D\\uDCCB User metadata:\", user.user_metadata);\n            // Get role from user metadata (set during signup)\n            const role = ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.role) || \"student\";\n            const fullName = ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.full_name) || \"\";\n            const email = user.email || ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.email) || \"\";\n            console.log(\"\\uD83D\\uDC64 User role:\", role);\n            // Check if profile already exists\n            const { data: existingProfile, error: profileCheckError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"id, role, full_name, email\").eq(\"id\", user.id).maybeSingle();\n            if (profileCheckError) {\n                console.error(\"❌ Error checking profile:\", profileCheckError);\n                throw new Error(\"Failed to check user profile: \" + profileCheckError.message);\n            }\n            // Create profile if it doesn't exist\n            if (!existingProfile) {\n                console.log(\"\\uD83D\\uDD28 Creating profile for user:\", user.id);\n                const { error: profileError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").insert({\n                    id: user.id,\n                    email: email,\n                    full_name: fullName,\n                    role: role\n                });\n                if (profileError) {\n                    console.error(\"❌ Error creating profile:\", profileError);\n                    throw new Error(\"Failed to create user profile: \" + profileError.message);\n                }\n                console.log(\"✅ Profile created successfully\");\n            } else {\n                console.log(\"✅ Profile already exists:\", existingProfile);\n            }\n            setStatus(\"success\");\n            setMessage(\"Email confirmed successfully! Redirecting to your dashboard...\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Welcome to EduBridge!\", {\n                description: \"Your email has been confirmed successfully.\"\n            });\n            // Redirect based on role after a short delay\n            setTimeout(()=>{\n                console.log(\"\\uD83D\\uDD04 Redirecting user with role:\", role);\n                if (role === \"admin\") {\n                    router.push(\"/admin/dashboard\");\n                } else if (role === \"teacher\") {\n                    router.push(\"/teacher/setup-profile\");\n                } else {\n                    router.push(\"/student/setup-profile\");\n                }\n            }, 1500);\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 Error handling successful auth:\", error);\n            setStatus(\"error\");\n            setMessage(\"Email confirmed but failed to set up your profile. Please try signing in manually.\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Profile Setup Failed\", {\n                description: \"Please try signing in manually or contact support.\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleEmailConfirmation = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDE80 Starting email confirmation process...\");\n                console.log(\"\\uD83D\\uDCCD Current URL:\", window.location.href);\n                // Add a small delay to ensure the page is fully loaded\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // First, check if user is already authenticated\n                const { data: { session }, error: sessionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                if (session === null || session === void 0 ? void 0 : session.user) {\n                    console.log(\"✅ User already authenticated:\", session.user.id);\n                    await handleSuccessfulAuth(session.user);\n                    return;\n                }\n                // Get URL parameters from both search and hash\n                const urlSearchParams = new URLSearchParams(window.location.search);\n                const urlHashParams = new URLSearchParams(window.location.hash.substring(1));\n                // Collect all possible parameters\n                const params = {\n                    code: urlSearchParams.get(\"code\") || urlHashParams.get(\"code\"),\n                    access_token: urlSearchParams.get(\"access_token\") || urlHashParams.get(\"access_token\"),\n                    refresh_token: urlSearchParams.get(\"refresh_token\") || urlHashParams.get(\"refresh_token\"),\n                    token_hash: urlSearchParams.get(\"token_hash\") || urlHashParams.get(\"token_hash\"),\n                    type: urlSearchParams.get(\"type\") || urlHashParams.get(\"type\"),\n                    error: urlSearchParams.get(\"error\") || urlHashParams.get(\"error\"),\n                    error_code: urlSearchParams.get(\"error_code\") || urlHashParams.get(\"error_code\"),\n                    error_description: urlSearchParams.get(\"error_description\") || urlHashParams.get(\"error_description\")\n                };\n                console.log(\"\\uD83D\\uDCCB URL Parameters found:\", params);\n                // Check for errors first\n                if (params.error) {\n                    console.error(\"❌ Email confirmation error:\", params.error);\n                    if (params.error_code === \"otp_expired\" || params.error === \"access_denied\") {\n                        setStatus(\"expired\");\n                        setMessage(\"Your email confirmation link has expired. Please request a new one.\");\n                        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Link Expired\", {\n                            description: \"Please request a new confirmation email below.\"\n                        });\n                    } else {\n                        setStatus(\"error\");\n                        setMessage(params.error_description || params.error);\n                        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                            description: params.error_description || params.error\n                        });\n                    }\n                    return;\n                }\n                let user = null;\n                // Method 1: PKCE Code Exchange (most common with newer Supabase)\n                if (params.code) {\n                    console.log(\"\\uD83D\\uDD04 Attempting PKCE code exchange...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.exchangeCodeForSession(params.code);\n                    if (error) {\n                        console.error(\"❌ PKCE failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ PKCE success:\", user.id);\n                    }\n                }\n                // Method 2: Direct session from hash parameters\n                if (!user && params.access_token && params.refresh_token) {\n                    console.log(\"\\uD83D\\uDD04 Attempting session from tokens...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.setSession({\n                        access_token: params.access_token,\n                        refresh_token: params.refresh_token\n                    });\n                    if (error) {\n                        console.error(\"❌ Session failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ Session success:\", user.id);\n                    }\n                }\n                // Method 3: OTP verification\n                if (!user && params.token_hash && params.type) {\n                    console.log(\"\\uD83D\\uDD04 Attempting OTP verification...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.verifyOtp({\n                        token_hash: params.token_hash,\n                        type: params.type\n                    });\n                    if (error) {\n                        console.error(\"❌ OTP failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ OTP success:\", user.id);\n                    }\n                }\n                // If no parameters found, try to get current session again\n                if (!user && !params.code && !params.access_token && !params.token_hash) {\n                    console.log(\"\\uD83D\\uDD04 No URL params found, checking session again...\");\n                    const { data: { session: retrySession } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                    if (retrySession === null || retrySession === void 0 ? void 0 : retrySession.user) {\n                        user = retrySession.user;\n                        console.log(\"✅ Found session on retry:\", user.id);\n                    }\n                }\n                if (user) {\n                    console.log(\"\\uD83C\\uDF89 Authentication successful, setting up profile...\");\n                    await handleSuccessfulAuth(user);\n                } else {\n                    console.log(\"❌ No authentication method worked\");\n                    setStatus(\"error\");\n                    setMessage(\"Unable to confirm your email. The link may be invalid or expired.\");\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                        description: \"Please try signing in or request a new confirmation email.\"\n                    });\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDCA5 Confirmation error:\", error);\n                setStatus(\"error\");\n                setMessage(\"An error occurred during email confirmation.\");\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                    description: \"Please try again or contact support.\"\n                });\n            }\n        };\n        // Start the confirmation process immediately\n        handleEmailConfirmation();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-edubridge-50 to-white flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-md w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-8 text-center\",\n                children: [\n                    status === \"loading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-16 w-16 text-edubridge-600 animate-spin mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirming Your Email\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"Please wait while we verify your email address...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.reload(),\n                                className: \"px-4 py-2 text-sm text-edubridge-600 hover:text-edubridge-800 underline\",\n                                children: \"Taking too long? Click to retry\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-16 w-16 text-green-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Email Confirmed!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Redirecting you to complete your profile...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-red-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirmation Failed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signin\"),\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors\",\n                                        children: \"Try Signing In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"expired\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-orange-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Link Expired\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"resend-email\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Enter your email to resend confirmation:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                id: \"resend-email\",\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                placeholder: \"<EMAIL>\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-edubridge-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleResendConfirmation,\n                                        disabled: isResending || !email,\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isResending ? \"Sending...\" : \"Resend Confirmation Email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n            lineNumber: 303,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfirmPage, \"SZaeJxz4oTkosS3Rij4vpgs5NYA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ConfirmPage;\nvar _c;\n$RefreshReg$(_c, \"ConfirmPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/auth/confirm/page.tsx\n"));

/***/ })

});