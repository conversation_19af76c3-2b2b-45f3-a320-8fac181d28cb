"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/confirm/page",{

/***/ "(app-pages-browser)/./app/auth/confirm/page.tsx":
/*!***********************************!*\
  !*** ./app/auth/confirm/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ConfirmPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ConfirmPage() {\n    _s();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"loading\");\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isResending, setIsResending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Add timeout to prevent infinite loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeout = setTimeout(()=>{\n            if (status === \"loading\") {\n                console.log(\"⏰ Confirmation timeout reached\");\n                setStatus(\"error\");\n                setMessage(\"Email confirmation is taking too long. Please try again.\");\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Timeout\", {\n                    description: \"Please try clicking the email link again.\"\n                });\n            }\n        }, 15000) // 15 second timeout\n        ;\n        return ()=>clearTimeout(timeout);\n    }, [\n        status\n    ]);\n    const handleResendConfirmation = async ()=>{\n        if (!email) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Email Required\", {\n                description: \"Please enter your email address to resend confirmation.\"\n            });\n            return;\n        }\n        setIsResending(true);\n        try {\n            // First try to resend confirmation\n            const { error: resendError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.resend({\n                type: \"signup\",\n                email: email,\n                options: {\n                    emailRedirectTo: \"\".concat(window.location.origin, \"/auth/confirm\")\n                }\n            });\n            if (resendError) {\n                console.log(\"Resend failed, trying signup:\", resendError.message);\n                // If resend fails, the user might not exist, so try signing them up again\n                const { error: signupError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signUp({\n                    email: email,\n                    password: \"TempPassword123!\",\n                    options: {\n                        emailRedirectTo: \"\".concat(window.location.origin, \"/auth/confirm\"),\n                        data: {\n                            role: \"student\",\n                            full_name: \"\"\n                        }\n                    }\n                });\n                if (signupError && !signupError.message.includes(\"already registered\")) {\n                    throw signupError;\n                }\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Email Sent!\", {\n                description: \"A new confirmation email has been sent. Please check your inbox and click the new link.\"\n            });\n            // Clear the email field and show success message\n            setEmail(\"\");\n            setMessage(\"A new confirmation email has been sent. Please check your inbox and click the new link.\");\n        } catch (error) {\n            console.error(\"Resend exception:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Resend Failed\", {\n                description: error.message || \"Failed to send confirmation email. Please try again.\"\n            });\n        } finally{\n            setIsResending(false);\n        }\n    };\n    const handleSuccessfulAuth = async (user)=>{\n        try {\n            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n            console.log(\"\\uD83C\\uDF89 Handling successful auth for user:\", user.id);\n            console.log(\"\\uD83D\\uDCCB User metadata:\", user.user_metadata);\n            // Get role from user metadata (set during signup)\n            const role = ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.role) || \"student\";\n            const fullName = ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.full_name) || \"\";\n            const email = user.email || ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.email) || \"\";\n            console.log(\"\\uD83D\\uDC64 User role:\", role);\n            // Check if profile already exists\n            const { data: existingProfile, error: profileCheckError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"id, role, full_name, email\").eq(\"id\", user.id).maybeSingle();\n            if (profileCheckError) {\n                console.error(\"❌ Error checking profile:\", profileCheckError);\n                throw new Error(\"Failed to check user profile: \" + profileCheckError.message);\n            }\n            // Create profile if it doesn't exist\n            if (!existingProfile) {\n                console.log(\"\\uD83D\\uDD28 Creating profile for user:\", user.id);\n                const { error: profileError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").insert({\n                    id: user.id,\n                    email: email,\n                    full_name: fullName,\n                    role: role\n                });\n                if (profileError) {\n                    console.error(\"❌ Error creating profile:\", profileError);\n                    throw new Error(\"Failed to create user profile: \" + profileError.message);\n                }\n                console.log(\"✅ Profile created successfully\");\n            } else {\n                console.log(\"✅ Profile already exists:\", existingProfile);\n            }\n            setStatus(\"success\");\n            setMessage(\"Email confirmed successfully! Redirecting to your dashboard...\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Welcome to EduBridge!\", {\n                description: \"Your email has been confirmed successfully.\"\n            });\n            // Redirect based on role after a short delay\n            setTimeout(()=>{\n                console.log(\"\\uD83D\\uDD04 Redirecting user with role:\", role);\n                if (role === \"admin\") {\n                    router.push(\"/admin/dashboard\");\n                } else if (role === \"teacher\") {\n                    router.push(\"/teacher/setup-profile\");\n                } else {\n                    router.push(\"/student/setup-profile\");\n                }\n            }, 1500);\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 Error handling successful auth:\", error);\n            setStatus(\"error\");\n            setMessage(\"Email confirmed but failed to set up your profile. Please try signing in manually.\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Profile Setup Failed\", {\n                description: \"Please try signing in manually or contact support.\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleEmailConfirmation = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDE80 Starting email confirmation process...\");\n                console.log(\"\\uD83D\\uDCCD Current URL:\", window.location.href);\n                // Add a small delay to ensure the page is fully loaded\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // First, check if user is already authenticated\n                const { data: { session }, error: sessionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                if (session === null || session === void 0 ? void 0 : session.user) {\n                    console.log(\"✅ User already authenticated:\", session.user.id);\n                    await handleSuccessfulAuth(session.user);\n                    return;\n                }\n                // Get URL parameters from both search and hash\n                const urlSearchParams = new URLSearchParams(window.location.search);\n                const urlHashParams = new URLSearchParams(window.location.hash.substring(1));\n                // Collect all possible parameters\n                const params = {\n                    code: urlSearchParams.get(\"code\") || urlHashParams.get(\"code\"),\n                    access_token: urlSearchParams.get(\"access_token\") || urlHashParams.get(\"access_token\"),\n                    refresh_token: urlSearchParams.get(\"refresh_token\") || urlHashParams.get(\"refresh_token\"),\n                    token_hash: urlSearchParams.get(\"token_hash\") || urlHashParams.get(\"token_hash\"),\n                    type: urlSearchParams.get(\"type\") || urlHashParams.get(\"type\"),\n                    error: urlSearchParams.get(\"error\") || urlHashParams.get(\"error\"),\n                    error_code: urlSearchParams.get(\"error_code\") || urlHashParams.get(\"error_code\"),\n                    error_description: urlSearchParams.get(\"error_description\") || urlHashParams.get(\"error_description\")\n                };\n                console.log(\"\\uD83D\\uDCCB URL Parameters found:\", params);\n                // Check for errors first\n                if (params.error) {\n                    console.error(\"❌ Email confirmation error:\", params.error);\n                    if (params.error_code === \"otp_expired\" || params.error === \"access_denied\") {\n                        setStatus(\"expired\");\n                        setMessage(\"Your email confirmation link has expired. Please request a new one.\");\n                        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Link Expired\", {\n                            description: \"Please request a new confirmation email below.\"\n                        });\n                    } else {\n                        setStatus(\"error\");\n                        setMessage(params.error_description || params.error);\n                        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                            description: params.error_description || params.error\n                        });\n                    }\n                    return;\n                }\n                let user = null;\n                // Method 1: PKCE Code Exchange (most common with newer Supabase)\n                if (params.code) {\n                    console.log(\"\\uD83D\\uDD04 Attempting PKCE code exchange...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.exchangeCodeForSession(params.code);\n                    if (error) {\n                        console.error(\"❌ PKCE failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ PKCE success:\", user.id);\n                    }\n                }\n                // Method 2: Direct session from hash parameters\n                if (!user && params.access_token && params.refresh_token) {\n                    console.log(\"\\uD83D\\uDD04 Attempting session from tokens...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.setSession({\n                        access_token: params.access_token,\n                        refresh_token: params.refresh_token\n                    });\n                    if (error) {\n                        console.error(\"❌ Session failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ Session success:\", user.id);\n                    }\n                }\n                // Method 3: OTP verification\n                if (!user && params.token_hash && params.type) {\n                    console.log(\"\\uD83D\\uDD04 Attempting OTP verification...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.verifyOtp({\n                        token_hash: params.token_hash,\n                        type: params.type\n                    });\n                    if (error) {\n                        console.error(\"❌ OTP failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ OTP success:\", user.id);\n                    }\n                }\n                // If no parameters found, try to get current session again\n                if (!user && !params.code && !params.access_token && !params.token_hash) {\n                    console.log(\"\\uD83D\\uDD04 No URL params found, checking session again...\");\n                    const { data: { session: retrySession } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                    if (retrySession === null || retrySession === void 0 ? void 0 : retrySession.user) {\n                        user = retrySession.user;\n                        console.log(\"✅ Found session on retry:\", user.id);\n                    }\n                }\n                if (user) {\n                    console.log(\"\\uD83C\\uDF89 Authentication successful, setting up profile...\");\n                    await handleSuccessfulAuth(user);\n                } else {\n                    console.log(\"❌ No authentication method worked\");\n                    setStatus(\"error\");\n                    setMessage(\"Unable to confirm your email. The link may be invalid or expired.\");\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                        description: \"Please try signing in or request a new confirmation email.\"\n                    });\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDCA5 Confirmation error:\", error);\n                setStatus(\"error\");\n                setMessage(\"An error occurred during email confirmation.\");\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                    description: \"Please try again or contact support.\"\n                });\n            }\n        };\n        // Start the confirmation process immediately\n        handleEmailConfirmation();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-edubridge-50 to-white flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-md w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-8 text-center\",\n                children: [\n                    status === \"loading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-16 w-16 text-edubridge-600 animate-spin mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirming Your Email\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"Please wait while we verify your email address...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.reload(),\n                                className: \"px-4 py-2 text-sm text-edubridge-600 hover:text-edubridge-800 underline\",\n                                children: \"Taking too long? Click to retry\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-16 w-16 text-green-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Email Confirmed!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Redirecting you to complete your profile...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-red-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirmation Failed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signin\"),\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors\",\n                                        children: \"Try Signing In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"expired\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-orange-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Link Expired\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"resend-email\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Enter your email to resend confirmation:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                id: \"resend-email\",\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                placeholder: \"<EMAIL>\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-edubridge-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleResendConfirmation,\n                                        disabled: isResending || !email,\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isResending ? \"Sending...\" : \"Resend Confirmation Email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signin\"),\n                                        className: \"w-full bg-blue-100 text-blue-700 py-2 px-4 rounded-md hover:bg-blue-200 transition-colors text-sm\",\n                                        children: \"Already have an account? Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n            lineNumber: 303,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfirmPage, \"SZaeJxz4oTkosS3Rij4vpgs5NYA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ConfirmPage;\nvar _c;\n$RefreshReg$(_c, \"ConfirmPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/auth/confirm/page.tsx\n"));

/***/ })

});