"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/confirm/page",{

/***/ "(app-pages-browser)/./app/auth/confirm/page.tsx":
/*!***********************************!*\
  !*** ./app/auth/confirm/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ConfirmPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ConfirmPage() {\n    _s();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"loading\");\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isResending, setIsResending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const handleResendConfirmation = async ()=>{\n        if (!email) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Email Required\", {\n                description: \"Please enter your email address to resend confirmation.\"\n            });\n            return;\n        }\n        setIsResending(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.resend({\n                type: \"signup\",\n                email: email,\n                options: {\n                    emailRedirectTo: \"\".concat(window.location.origin, \"/auth/confirm\")\n                }\n            });\n            if (error) {\n                console.error(\"Resend error:\", error);\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Resend Failed\", {\n                    description: error.message\n                });\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Email Sent\", {\n                    description: \"A new confirmation email has been sent. Please check your inbox.\"\n                });\n                setStatus(\"loading\");\n                setMessage(\"A new confirmation email has been sent. Please check your inbox and click the new link.\");\n            }\n        } catch (error) {\n            console.error(\"Resend exception:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Resend Failed\", {\n                description: \"Failed to resend confirmation email.\"\n            });\n        } finally{\n            setIsResending(false);\n        }\n    };\n    const handleSuccessfulAuth = async (user)=>{\n        try {\n            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n            console.log(\"\\uD83C\\uDF89 Handling successful auth for user:\", user.id);\n            console.log(\"\\uD83D\\uDCCB User metadata:\", user.user_metadata);\n            // Get role from user metadata (set during signup)\n            const role = ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.role) || \"student\";\n            const fullName = ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.full_name) || \"\";\n            const email = user.email || ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.email) || \"\";\n            console.log(\"\\uD83D\\uDC64 User role:\", role);\n            // Check if profile already exists\n            const { data: existingProfile, error: profileCheckError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"id, role, full_name, email\").eq(\"id\", user.id).maybeSingle();\n            if (profileCheckError) {\n                console.error(\"❌ Error checking profile:\", profileCheckError);\n                throw new Error(\"Failed to check user profile: \" + profileCheckError.message);\n            }\n            // Create profile if it doesn't exist\n            if (!existingProfile) {\n                console.log(\"\\uD83D\\uDD28 Creating profile for user:\", user.id);\n                const { error: profileError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").insert({\n                    id: user.id,\n                    email: email,\n                    full_name: fullName,\n                    role: role\n                });\n                if (profileError) {\n                    console.error(\"❌ Error creating profile:\", profileError);\n                    throw new Error(\"Failed to create user profile: \" + profileError.message);\n                }\n                console.log(\"✅ Profile created successfully\");\n            } else {\n                console.log(\"✅ Profile already exists:\", existingProfile);\n            }\n            setStatus(\"success\");\n            setMessage(\"Email confirmed successfully! Redirecting to your dashboard...\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Welcome to EduBridge!\", {\n                description: \"Your email has been confirmed successfully.\"\n            });\n            // Redirect based on role after a short delay\n            setTimeout(()=>{\n                console.log(\"\\uD83D\\uDD04 Redirecting user with role:\", role);\n                if (role === \"admin\") {\n                    router.push(\"/admin/dashboard\");\n                } else if (role === \"teacher\") {\n                    router.push(\"/teacher/setup-profile\");\n                } else {\n                    router.push(\"/student/setup-profile\");\n                }\n            }, 1500);\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 Error handling successful auth:\", error);\n            setStatus(\"error\");\n            setMessage(\"Email confirmed but failed to set up your profile. Please try signing in manually.\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Profile Setup Failed\", {\n                description: \"Please try signing in manually or contact support.\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleEmailConfirmation = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDE80 Starting email confirmation process...\");\n                console.log(\"\\uD83D\\uDCCD Current URL:\", window.location.href);\n                // Add a small delay to ensure the page is fully loaded\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // First, check if user is already authenticated\n                const { data: { session }, error: sessionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                if (session === null || session === void 0 ? void 0 : session.user) {\n                    console.log(\"✅ User already authenticated:\", session.user.id);\n                    await handleSuccessfulAuth(session.user);\n                    return;\n                }\n                // Get URL parameters from both search and hash\n                const urlSearchParams = new URLSearchParams(window.location.search);\n                const urlHashParams = new URLSearchParams(window.location.hash.substring(1));\n                // Collect all possible parameters\n                const params = {\n                    code: urlSearchParams.get(\"code\") || urlHashParams.get(\"code\"),\n                    access_token: urlSearchParams.get(\"access_token\") || urlHashParams.get(\"access_token\"),\n                    refresh_token: urlSearchParams.get(\"refresh_token\") || urlHashParams.get(\"refresh_token\"),\n                    token_hash: urlSearchParams.get(\"token_hash\") || urlHashParams.get(\"token_hash\"),\n                    type: urlSearchParams.get(\"type\") || urlHashParams.get(\"type\"),\n                    error: urlSearchParams.get(\"error\") || urlHashParams.get(\"error\"),\n                    error_code: urlSearchParams.get(\"error_code\") || urlHashParams.get(\"error_code\"),\n                    error_description: urlSearchParams.get(\"error_description\") || urlHashParams.get(\"error_description\")\n                };\n                console.log(\"\\uD83D\\uDCCB URL Parameters found:\", params);\n                // Check for errors first\n                if (params.error) {\n                    console.error(\"❌ Email confirmation error:\", params.error);\n                    setStatus(\"error\");\n                    setMessage(params.error_description || params.error);\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                        description: params.error_description || params.error\n                    });\n                    return;\n                }\n                let user = null;\n                // Method 1: PKCE Code Exchange (most common with newer Supabase)\n                if (params.code) {\n                    console.log(\"\\uD83D\\uDD04 Attempting PKCE code exchange...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.exchangeCodeForSession(params.code);\n                    if (error) {\n                        console.error(\"❌ PKCE failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ PKCE success:\", user.id);\n                    }\n                }\n                // Method 2: Direct session from hash parameters\n                if (!user && params.access_token && params.refresh_token) {\n                    console.log(\"\\uD83D\\uDD04 Attempting session from tokens...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.setSession({\n                        access_token: params.access_token,\n                        refresh_token: params.refresh_token\n                    });\n                    if (error) {\n                        console.error(\"❌ Session failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ Session success:\", user.id);\n                    }\n                }\n                // Method 3: OTP verification\n                if (!user && params.token_hash && params.type) {\n                    console.log(\"\\uD83D\\uDD04 Attempting OTP verification...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.verifyOtp({\n                        token_hash: params.token_hash,\n                        type: params.type\n                    });\n                    if (error) {\n                        console.error(\"❌ OTP failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ OTP success:\", user.id);\n                    }\n                }\n                // If no parameters found, try to get current session again\n                if (!user && !params.code && !params.access_token && !params.token_hash) {\n                    console.log(\"\\uD83D\\uDD04 No URL params found, checking session again...\");\n                    const { data: { session: retrySession } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                    if (retrySession === null || retrySession === void 0 ? void 0 : retrySession.user) {\n                        user = retrySession.user;\n                        console.log(\"✅ Found session on retry:\", user.id);\n                    }\n                }\n                if (user) {\n                    console.log(\"\\uD83C\\uDF89 Authentication successful, setting up profile...\");\n                    await handleSuccessfulAuth(user);\n                } else {\n                    console.log(\"❌ No authentication method worked\");\n                    setStatus(\"error\");\n                    setMessage(\"Unable to confirm your email. The link may be invalid or expired.\");\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                        description: \"Please try signing in or request a new confirmation email.\"\n                    });\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDCA5 Confirmation error:\", error);\n                setStatus(\"error\");\n                setMessage(\"An error occurred during email confirmation.\");\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                    description: \"Please try again or contact support.\"\n                });\n            }\n        };\n        // Start the confirmation process immediately\n        handleEmailConfirmation();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-edubridge-50 to-white flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-md w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-8 text-center\",\n                children: [\n                    status === \"loading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-16 w-16 text-edubridge-600 animate-spin mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirming Your Email\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Please wait while we verify your email address...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-16 w-16 text-green-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Email Confirmed!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Redirecting you to complete your profile...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-red-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirmation Failed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signin\"),\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors\",\n                                        children: \"Try Signing In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"expired\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-orange-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Link Expired\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"resend-email\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Enter your email to resend confirmation:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                id: \"resend-email\",\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                placeholder: \"<EMAIL>\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-edubridge-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleResendConfirmation,\n                                        disabled: isResending || !email,\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isResending ? \"Sending...\" : \"Resend Confirmation Email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfirmPage, \"q7SLdU1bNE6DNR7s5iHRxwvjeXk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ConfirmPage;\nvar _c;\n$RefreshReg$(_c, \"ConfirmPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/auth/confirm/page.tsx\n"));

/***/ })

});