"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/confirm/page",{

/***/ "(app-pages-browser)/./app/auth/confirm/page.tsx":
/*!***********************************!*\
  !*** ./app/auth/confirm/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ConfirmPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ConfirmPage() {\n    _s();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"loading\");\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isResending, setIsResending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Add timeout to prevent infinite loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeout = setTimeout(()=>{\n            if (status === \"loading\") {\n                console.log(\"⏰ Confirmation timeout reached\");\n                setStatus(\"error\");\n                setMessage(\"Email confirmation is taking too long. Please try again.\");\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Timeout\", {\n                    description: \"Please try clicking the email link again.\"\n                });\n            }\n        }, 15000) // 15 second timeout\n        ;\n        return ()=>clearTimeout(timeout);\n    }, [\n        status\n    ]);\n    const handleResendConfirmation = async ()=>{\n        if (!email) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Email Required\", {\n                description: \"Please enter your email address to resend confirmation.\"\n            });\n            return;\n        }\n        setIsResending(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.resend({\n                type: \"signup\",\n                email: email,\n                options: {\n                    emailRedirectTo: \"\".concat(window.location.origin, \"/auth/confirm\")\n                }\n            });\n            if (error) {\n                console.error(\"Resend error:\", error);\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Resend Failed\", {\n                    description: error.message\n                });\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Email Sent\", {\n                    description: \"A new confirmation email has been sent. Please check your inbox.\"\n                });\n                setStatus(\"loading\");\n                setMessage(\"A new confirmation email has been sent. Please check your inbox and click the new link.\");\n            }\n        } catch (error) {\n            console.error(\"Resend exception:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Resend Failed\", {\n                description: \"Failed to resend confirmation email.\"\n            });\n        } finally{\n            setIsResending(false);\n        }\n    };\n    const handleSuccessfulAuth = async (user)=>{\n        try {\n            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n            console.log(\"\\uD83C\\uDF89 Handling successful auth for user:\", user.id);\n            console.log(\"\\uD83D\\uDCCB User metadata:\", user.user_metadata);\n            // Get role from user metadata (set during signup)\n            const role = ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.role) || \"student\";\n            const fullName = ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.full_name) || \"\";\n            const email = user.email || ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.email) || \"\";\n            console.log(\"\\uD83D\\uDC64 User role:\", role);\n            // Check if profile already exists\n            const { data: existingProfile, error: profileCheckError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"id, role, full_name, email\").eq(\"id\", user.id).maybeSingle();\n            if (profileCheckError) {\n                console.error(\"❌ Error checking profile:\", profileCheckError);\n                throw new Error(\"Failed to check user profile: \" + profileCheckError.message);\n            }\n            // Create profile if it doesn't exist\n            if (!existingProfile) {\n                console.log(\"\\uD83D\\uDD28 Creating profile for user:\", user.id);\n                const { error: profileError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").insert({\n                    id: user.id,\n                    email: email,\n                    full_name: fullName,\n                    role: role\n                });\n                if (profileError) {\n                    console.error(\"❌ Error creating profile:\", profileError);\n                    throw new Error(\"Failed to create user profile: \" + profileError.message);\n                }\n                console.log(\"✅ Profile created successfully\");\n            } else {\n                console.log(\"✅ Profile already exists:\", existingProfile);\n            }\n            setStatus(\"success\");\n            setMessage(\"Email confirmed successfully! Redirecting to your dashboard...\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Welcome to EduBridge!\", {\n                description: \"Your email has been confirmed successfully.\"\n            });\n            // Redirect based on role after a short delay\n            setTimeout(()=>{\n                console.log(\"\\uD83D\\uDD04 Redirecting user with role:\", role);\n                if (role === \"admin\") {\n                    router.push(\"/admin/dashboard\");\n                } else if (role === \"teacher\") {\n                    router.push(\"/teacher/setup-profile\");\n                } else {\n                    router.push(\"/student/setup-profile\");\n                }\n            }, 1500);\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 Error handling successful auth:\", error);\n            setStatus(\"error\");\n            setMessage(\"Email confirmed but failed to set up your profile. Please try signing in manually.\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Profile Setup Failed\", {\n                description: \"Please try signing in manually or contact support.\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleEmailConfirmation = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDE80 Starting email confirmation process...\");\n                console.log(\"\\uD83D\\uDCCD Current URL:\", window.location.href);\n                // Add a small delay to ensure the page is fully loaded\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // First, check if user is already authenticated\n                const { data: { session }, error: sessionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                if (session === null || session === void 0 ? void 0 : session.user) {\n                    console.log(\"✅ User already authenticated:\", session.user.id);\n                    await handleSuccessfulAuth(session.user);\n                    return;\n                }\n                // Get URL parameters from both search and hash\n                const urlSearchParams = new URLSearchParams(window.location.search);\n                const urlHashParams = new URLSearchParams(window.location.hash.substring(1));\n                // Collect all possible parameters\n                const params = {\n                    code: urlSearchParams.get(\"code\") || urlHashParams.get(\"code\"),\n                    access_token: urlSearchParams.get(\"access_token\") || urlHashParams.get(\"access_token\"),\n                    refresh_token: urlSearchParams.get(\"refresh_token\") || urlHashParams.get(\"refresh_token\"),\n                    token_hash: urlSearchParams.get(\"token_hash\") || urlHashParams.get(\"token_hash\"),\n                    type: urlSearchParams.get(\"type\") || urlHashParams.get(\"type\"),\n                    error: urlSearchParams.get(\"error\") || urlHashParams.get(\"error\"),\n                    error_code: urlSearchParams.get(\"error_code\") || urlHashParams.get(\"error_code\"),\n                    error_description: urlSearchParams.get(\"error_description\") || urlHashParams.get(\"error_description\")\n                };\n                console.log(\"\\uD83D\\uDCCB URL Parameters found:\", params);\n                // Check for errors first\n                if (params.error) {\n                    console.error(\"❌ Email confirmation error:\", params.error);\n                    setStatus(\"error\");\n                    setMessage(params.error_description || params.error);\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                        description: params.error_description || params.error\n                    });\n                    return;\n                }\n                let user = null;\n                // Method 1: PKCE Code Exchange (most common with newer Supabase)\n                if (params.code) {\n                    console.log(\"\\uD83D\\uDD04 Attempting PKCE code exchange...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.exchangeCodeForSession(params.code);\n                    if (error) {\n                        console.error(\"❌ PKCE failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ PKCE success:\", user.id);\n                    }\n                }\n                // Method 2: Direct session from hash parameters\n                if (!user && params.access_token && params.refresh_token) {\n                    console.log(\"\\uD83D\\uDD04 Attempting session from tokens...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.setSession({\n                        access_token: params.access_token,\n                        refresh_token: params.refresh_token\n                    });\n                    if (error) {\n                        console.error(\"❌ Session failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ Session success:\", user.id);\n                    }\n                }\n                // Method 3: OTP verification\n                if (!user && params.token_hash && params.type) {\n                    console.log(\"\\uD83D\\uDD04 Attempting OTP verification...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.verifyOtp({\n                        token_hash: params.token_hash,\n                        type: params.type\n                    });\n                    if (error) {\n                        console.error(\"❌ OTP failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ OTP success:\", user.id);\n                    }\n                }\n                // If no parameters found, try to get current session again\n                if (!user && !params.code && !params.access_token && !params.token_hash) {\n                    console.log(\"\\uD83D\\uDD04 No URL params found, checking session again...\");\n                    const { data: { session: retrySession } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                    if (retrySession === null || retrySession === void 0 ? void 0 : retrySession.user) {\n                        user = retrySession.user;\n                        console.log(\"✅ Found session on retry:\", user.id);\n                    }\n                }\n                if (user) {\n                    console.log(\"\\uD83C\\uDF89 Authentication successful, setting up profile...\");\n                    await handleSuccessfulAuth(user);\n                } else {\n                    console.log(\"❌ No authentication method worked\");\n                    setStatus(\"error\");\n                    setMessage(\"Unable to confirm your email. The link may be invalid or expired.\");\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                        description: \"Please try signing in or request a new confirmation email.\"\n                    });\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDCA5 Confirmation error:\", error);\n                setStatus(\"error\");\n                setMessage(\"An error occurred during email confirmation.\");\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                    description: \"Please try again or contact support.\"\n                });\n            }\n        };\n        // Start the confirmation process immediately\n        handleEmailConfirmation();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-edubridge-50 to-white flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-md w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-8 text-center\",\n                children: [\n                    status === \"loading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-16 w-16 text-edubridge-600 animate-spin mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirming Your Email\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Please wait while we verify your email address...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-16 w-16 text-green-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Email Confirmed!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Redirecting you to complete your profile...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-red-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirmation Failed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signin\"),\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors\",\n                                        children: \"Try Signing In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"expired\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-orange-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Link Expired\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"resend-email\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Enter your email to resend confirmation:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                id: \"resend-email\",\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                placeholder: \"<EMAIL>\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-edubridge-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleResendConfirmation,\n                                        disabled: isResending || !email,\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isResending ? \"Sending...\" : \"Resend Confirmation Email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfirmPage, \"SZaeJxz4oTkosS3Rij4vpgs5NYA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ConfirmPage;\nvar _c;\n$RefreshReg$(_c, \"ConfirmPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/auth/confirm/page.tsx\n"));

/***/ })

});