"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/confirm/page",{

/***/ "(app-pages-browser)/./app/auth/confirm/page.tsx":
/*!***********************************!*\
  !*** ./app/auth/confirm/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ConfirmPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ConfirmPage() {\n    _s();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"loading\");\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isResending, setIsResending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Add timeout to prevent infinite loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeout = setTimeout(()=>{\n            if (status === \"loading\") {\n                console.log(\"⏰ Confirmation timeout reached\");\n                setStatus(\"error\");\n                setMessage(\"Email confirmation is taking too long. Please try again.\");\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Timeout\", {\n                    description: \"Please try clicking the email link again.\"\n                });\n            }\n        }, 15000) // 15 second timeout\n        ;\n        return ()=>clearTimeout(timeout);\n    }, [\n        status\n    ]);\n    const handleResendConfirmation = async ()=>{\n        if (!email) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Email Required\", {\n                description: \"Please enter your email address to resend confirmation.\"\n            });\n            return;\n        }\n        setIsResending(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.resend({\n                type: \"signup\",\n                email: email,\n                options: {\n                    emailRedirectTo: \"\".concat(window.location.origin, \"/auth/confirm\")\n                }\n            });\n            if (error) {\n                console.error(\"Resend error:\", error);\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Resend Failed\", {\n                    description: error.message\n                });\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Email Sent\", {\n                    description: \"A new confirmation email has been sent. Please check your inbox.\"\n                });\n                setStatus(\"loading\");\n                setMessage(\"A new confirmation email has been sent. Please check your inbox and click the new link.\");\n            }\n        } catch (error) {\n            console.error(\"Resend exception:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Resend Failed\", {\n                description: \"Failed to resend confirmation email.\"\n            });\n        } finally{\n            setIsResending(false);\n        }\n    };\n    const handleSuccessfulAuth = async (user)=>{\n        try {\n            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n            console.log(\"\\uD83C\\uDF89 Handling successful auth for user:\", user.id);\n            console.log(\"\\uD83D\\uDCCB User metadata:\", user.user_metadata);\n            // Get role from user metadata (set during signup)\n            const role = ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.role) || \"student\";\n            const fullName = ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.full_name) || \"\";\n            const email = user.email || ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.email) || \"\";\n            console.log(\"\\uD83D\\uDC64 User role:\", role);\n            // Check if profile already exists\n            const { data: existingProfile, error: profileCheckError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"id, role, full_name, email\").eq(\"id\", user.id).maybeSingle();\n            if (profileCheckError) {\n                console.error(\"❌ Error checking profile:\", profileCheckError);\n                throw new Error(\"Failed to check user profile: \" + profileCheckError.message);\n            }\n            // Create profile if it doesn't exist\n            if (!existingProfile) {\n                console.log(\"\\uD83D\\uDD28 Creating profile for user:\", user.id);\n                const { error: profileError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").insert({\n                    id: user.id,\n                    email: email,\n                    full_name: fullName,\n                    role: role\n                });\n                if (profileError) {\n                    console.error(\"❌ Error creating profile:\", profileError);\n                    throw new Error(\"Failed to create user profile: \" + profileError.message);\n                }\n                console.log(\"✅ Profile created successfully\");\n            } else {\n                console.log(\"✅ Profile already exists:\", existingProfile);\n            }\n            setStatus(\"success\");\n            setMessage(\"Email confirmed successfully! Redirecting to your dashboard...\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Welcome to EduBridge!\", {\n                description: \"Your email has been confirmed successfully.\"\n            });\n            // Redirect based on role after a short delay\n            setTimeout(()=>{\n                console.log(\"\\uD83D\\uDD04 Redirecting user with role:\", role);\n                if (role === \"admin\") {\n                    router.push(\"/admin/dashboard\");\n                } else if (role === \"teacher\") {\n                    router.push(\"/teacher/setup-profile\");\n                } else {\n                    router.push(\"/student/setup-profile\");\n                }\n            }, 1500);\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 Error handling successful auth:\", error);\n            setStatus(\"error\");\n            setMessage(\"Email confirmed but failed to set up your profile. Please try signing in manually.\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Profile Setup Failed\", {\n                description: \"Please try signing in manually or contact support.\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleEmailConfirmation = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDE80 Starting email confirmation process...\");\n                console.log(\"\\uD83D\\uDCCD Current URL:\", window.location.href);\n                // Add a small delay to ensure the page is fully loaded\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // First, check if user is already authenticated\n                const { data: { session }, error: sessionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                if (session === null || session === void 0 ? void 0 : session.user) {\n                    console.log(\"✅ User already authenticated:\", session.user.id);\n                    await handleSuccessfulAuth(session.user);\n                    return;\n                }\n                // Get URL parameters from both search and hash\n                const urlSearchParams = new URLSearchParams(window.location.search);\n                const urlHashParams = new URLSearchParams(window.location.hash.substring(1));\n                // Collect all possible parameters\n                const params = {\n                    code: urlSearchParams.get(\"code\") || urlHashParams.get(\"code\"),\n                    access_token: urlSearchParams.get(\"access_token\") || urlHashParams.get(\"access_token\"),\n                    refresh_token: urlSearchParams.get(\"refresh_token\") || urlHashParams.get(\"refresh_token\"),\n                    token_hash: urlSearchParams.get(\"token_hash\") || urlHashParams.get(\"token_hash\"),\n                    type: urlSearchParams.get(\"type\") || urlHashParams.get(\"type\"),\n                    error: urlSearchParams.get(\"error\") || urlHashParams.get(\"error\"),\n                    error_code: urlSearchParams.get(\"error_code\") || urlHashParams.get(\"error_code\"),\n                    error_description: urlSearchParams.get(\"error_description\") || urlHashParams.get(\"error_description\")\n                };\n                console.log(\"\\uD83D\\uDCCB URL Parameters found:\", params);\n                // Check for errors first\n                if (params.error) {\n                    console.error(\"❌ Email confirmation error:\", params.error);\n                    setStatus(\"error\");\n                    setMessage(params.error_description || params.error);\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                        description: params.error_description || params.error\n                    });\n                    return;\n                }\n                let user = null;\n                // Method 1: PKCE Code Exchange (most common with newer Supabase)\n                if (params.code) {\n                    console.log(\"\\uD83D\\uDD04 Attempting PKCE code exchange...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.exchangeCodeForSession(params.code);\n                    if (error) {\n                        console.error(\"❌ PKCE failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ PKCE success:\", user.id);\n                    }\n                }\n                // Method 2: Direct session from hash parameters\n                if (!user && params.access_token && params.refresh_token) {\n                    console.log(\"\\uD83D\\uDD04 Attempting session from tokens...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.setSession({\n                        access_token: params.access_token,\n                        refresh_token: params.refresh_token\n                    });\n                    if (error) {\n                        console.error(\"❌ Session failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ Session success:\", user.id);\n                    }\n                }\n                // Method 3: OTP verification\n                if (!user && params.token_hash && params.type) {\n                    console.log(\"\\uD83D\\uDD04 Attempting OTP verification...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.verifyOtp({\n                        token_hash: params.token_hash,\n                        type: params.type\n                    });\n                    if (error) {\n                        console.error(\"❌ OTP failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ OTP success:\", user.id);\n                    }\n                }\n                // If no parameters found, try to get current session again\n                if (!user && !params.code && !params.access_token && !params.token_hash) {\n                    console.log(\"\\uD83D\\uDD04 No URL params found, checking session again...\");\n                    const { data: { session: retrySession } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                    if (retrySession === null || retrySession === void 0 ? void 0 : retrySession.user) {\n                        user = retrySession.user;\n                        console.log(\"✅ Found session on retry:\", user.id);\n                    }\n                }\n                if (user) {\n                    console.log(\"\\uD83C\\uDF89 Authentication successful, setting up profile...\");\n                    await handleSuccessfulAuth(user);\n                } else {\n                    console.log(\"❌ No authentication method worked\");\n                    setStatus(\"error\");\n                    setMessage(\"Unable to confirm your email. The link may be invalid or expired.\");\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                        description: \"Please try signing in or request a new confirmation email.\"\n                    });\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDCA5 Confirmation error:\", error);\n                setStatus(\"error\");\n                setMessage(\"An error occurred during email confirmation.\");\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                    description: \"Please try again or contact support.\"\n                });\n            }\n        };\n        // Start the confirmation process immediately\n        handleEmailConfirmation();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-edubridge-50 to-white flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-md w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-8 text-center\",\n                children: [\n                    status === \"loading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-16 w-16 text-edubridge-600 animate-spin mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirming Your Email\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"Please wait while we verify your email address...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.reload(),\n                                className: \"px-4 py-2 text-sm text-edubridge-600 hover:text-edubridge-800 underline\",\n                                children: \"Taking too long? Click to retry\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-16 w-16 text-green-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Email Confirmed!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Redirecting you to complete your profile...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-red-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirmation Failed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signin\"),\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors\",\n                                        children: \"Try Signing In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"expired\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-orange-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Link Expired\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"resend-email\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Enter your email to resend confirmation:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                id: \"resend-email\",\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                placeholder: \"<EMAIL>\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-edubridge-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleResendConfirmation,\n                                        disabled: isResending || !email,\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isResending ? \"Sending...\" : \"Resend Confirmation Email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfirmPage, \"SZaeJxz4oTkosS3Rij4vpgs5NYA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ConfirmPage;\nvar _c;\n$RefreshReg$(_c, \"ConfirmPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/auth/confirm/page.tsx\n"));

/***/ })

});