"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/confirm/page",{

/***/ "(app-pages-browser)/./app/auth/confirm/page.tsx":
/*!***********************************!*\
  !*** ./app/auth/confirm/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ConfirmPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ConfirmPage() {\n    _s();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"loading\");\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isResending, setIsResending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Add timeout to prevent infinite loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeout = setTimeout(()=>{\n            if (status === \"loading\") {\n                console.log(\"⏰ Confirmation timeout reached\");\n                setStatus(\"error\");\n                setMessage(\"Email confirmation is taking too long. Please try again.\");\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Timeout\", {\n                    description: \"Please try clicking the email link again.\"\n                });\n            }\n        }, 15000) // 15 second timeout\n        ;\n        return ()=>clearTimeout(timeout);\n    }, [\n        status\n    ]);\n    const handleResendConfirmation = async ()=>{\n        if (!email) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Email Required\", {\n                description: \"Please enter your email address to resend confirmation.\"\n            });\n            return;\n        }\n        setIsResending(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.resend({\n                type: \"signup\",\n                email: email,\n                options: {\n                    emailRedirectTo: \"\".concat(window.location.origin, \"/auth/confirm\")\n                }\n            });\n            if (error) {\n                console.error(\"Resend error:\", error);\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Resend Failed\", {\n                    description: error.message\n                });\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Email Sent\", {\n                    description: \"A new confirmation email has been sent. Please check your inbox.\"\n                });\n                setStatus(\"loading\");\n                setMessage(\"A new confirmation email has been sent. Please check your inbox and click the new link.\");\n            }\n        } catch (error) {\n            console.error(\"Resend exception:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Resend Failed\", {\n                description: \"Failed to resend confirmation email.\"\n            });\n        } finally{\n            setIsResending(false);\n        }\n    };\n    const handleSuccessfulAuth = async (user)=>{\n        try {\n            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n            console.log(\"\\uD83C\\uDF89 Handling successful auth for user:\", user.id);\n            console.log(\"\\uD83D\\uDCCB User metadata:\", user.user_metadata);\n            // Get role from user metadata (set during signup)\n            const role = ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.role) || \"student\";\n            const fullName = ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.full_name) || \"\";\n            const email = user.email || ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.email) || \"\";\n            console.log(\"\\uD83D\\uDC64 User role:\", role);\n            // Check if profile already exists\n            const { data: existingProfile, error: profileCheckError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"id, role, full_name, email\").eq(\"id\", user.id).maybeSingle();\n            if (profileCheckError) {\n                console.error(\"❌ Error checking profile:\", profileCheckError);\n                throw new Error(\"Failed to check user profile: \" + profileCheckError.message);\n            }\n            // Create profile if it doesn't exist\n            if (!existingProfile) {\n                console.log(\"\\uD83D\\uDD28 Creating profile for user:\", user.id);\n                const { error: profileError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").insert({\n                    id: user.id,\n                    email: email,\n                    full_name: fullName,\n                    role: role\n                });\n                if (profileError) {\n                    console.error(\"❌ Error creating profile:\", profileError);\n                    throw new Error(\"Failed to create user profile: \" + profileError.message);\n                }\n                console.log(\"✅ Profile created successfully\");\n            } else {\n                console.log(\"✅ Profile already exists:\", existingProfile);\n            }\n            setStatus(\"success\");\n            setMessage(\"Email confirmed successfully! Redirecting to your dashboard...\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Welcome to EduBridge!\", {\n                description: \"Your email has been confirmed successfully.\"\n            });\n            // Redirect based on role after a short delay\n            setTimeout(()=>{\n                console.log(\"\\uD83D\\uDD04 Redirecting user with role:\", role);\n                if (role === \"admin\") {\n                    router.push(\"/admin/dashboard\");\n                } else if (role === \"teacher\") {\n                    router.push(\"/teacher/setup-profile\");\n                } else {\n                    router.push(\"/student/setup-profile\");\n                }\n            }, 1500);\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 Error handling successful auth:\", error);\n            setStatus(\"error\");\n            setMessage(\"Email confirmed but failed to set up your profile. Please try signing in manually.\");\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Profile Setup Failed\", {\n                description: \"Please try signing in manually or contact support.\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleEmailConfirmation = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDE80 Starting email confirmation process...\");\n                console.log(\"\\uD83D\\uDCCD Current URL:\", window.location.href);\n                // Add a small delay to ensure the page is fully loaded\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // First, check if user is already authenticated\n                const { data: { session }, error: sessionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                if (session === null || session === void 0 ? void 0 : session.user) {\n                    console.log(\"✅ User already authenticated:\", session.user.id);\n                    await handleSuccessfulAuth(session.user);\n                    return;\n                }\n                // Get URL parameters from both search and hash\n                const urlSearchParams = new URLSearchParams(window.location.search);\n                const urlHashParams = new URLSearchParams(window.location.hash.substring(1));\n                // Collect all possible parameters\n                const params = {\n                    code: urlSearchParams.get(\"code\") || urlHashParams.get(\"code\"),\n                    access_token: urlSearchParams.get(\"access_token\") || urlHashParams.get(\"access_token\"),\n                    refresh_token: urlSearchParams.get(\"refresh_token\") || urlHashParams.get(\"refresh_token\"),\n                    token_hash: urlSearchParams.get(\"token_hash\") || urlHashParams.get(\"token_hash\"),\n                    type: urlSearchParams.get(\"type\") || urlHashParams.get(\"type\"),\n                    error: urlSearchParams.get(\"error\") || urlHashParams.get(\"error\"),\n                    error_code: urlSearchParams.get(\"error_code\") || urlHashParams.get(\"error_code\"),\n                    error_description: urlSearchParams.get(\"error_description\") || urlHashParams.get(\"error_description\")\n                };\n                console.log(\"\\uD83D\\uDCCB URL Parameters found:\", params);\n                // Check for errors first\n                if (params.error) {\n                    console.error(\"❌ Email confirmation error:\", params.error);\n                    if (params.error_code === \"otp_expired\" || params.error === \"access_denied\") {\n                        setStatus(\"expired\");\n                        setMessage(\"Your email confirmation link has expired. Please request a new one.\");\n                        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Link Expired\", {\n                            description: \"Please request a new confirmation email below.\"\n                        });\n                    } else {\n                        setStatus(\"error\");\n                        setMessage(params.error_description || params.error);\n                        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                            description: params.error_description || params.error\n                        });\n                    }\n                    return;\n                }\n                let user = null;\n                // Method 1: PKCE Code Exchange (most common with newer Supabase)\n                if (params.code) {\n                    console.log(\"\\uD83D\\uDD04 Attempting PKCE code exchange...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.exchangeCodeForSession(params.code);\n                    if (error) {\n                        console.error(\"❌ PKCE failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ PKCE success:\", user.id);\n                    }\n                }\n                // Method 2: Direct session from hash parameters\n                if (!user && params.access_token && params.refresh_token) {\n                    console.log(\"\\uD83D\\uDD04 Attempting session from tokens...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.setSession({\n                        access_token: params.access_token,\n                        refresh_token: params.refresh_token\n                    });\n                    if (error) {\n                        console.error(\"❌ Session failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ Session success:\", user.id);\n                    }\n                }\n                // Method 3: OTP verification\n                if (!user && params.token_hash && params.type) {\n                    console.log(\"\\uD83D\\uDD04 Attempting OTP verification...\");\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.verifyOtp({\n                        token_hash: params.token_hash,\n                        type: params.type\n                    });\n                    if (error) {\n                        console.error(\"❌ OTP failed:\", error);\n                    } else if (data === null || data === void 0 ? void 0 : data.user) {\n                        user = data.user;\n                        console.log(\"✅ OTP success:\", user.id);\n                    }\n                }\n                // If no parameters found, try to get current session again\n                if (!user && !params.code && !params.access_token && !params.token_hash) {\n                    console.log(\"\\uD83D\\uDD04 No URL params found, checking session again...\");\n                    const { data: { session: retrySession } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                    if (retrySession === null || retrySession === void 0 ? void 0 : retrySession.user) {\n                        user = retrySession.user;\n                        console.log(\"✅ Found session on retry:\", user.id);\n                    }\n                }\n                if (user) {\n                    console.log(\"\\uD83C\\uDF89 Authentication successful, setting up profile...\");\n                    await handleSuccessfulAuth(user);\n                } else {\n                    console.log(\"❌ No authentication method worked\");\n                    setStatus(\"error\");\n                    setMessage(\"Unable to confirm your email. The link may be invalid or expired.\");\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                        description: \"Please try signing in or request a new confirmation email.\"\n                    });\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDCA5 Confirmation error:\", error);\n                setStatus(\"error\");\n                setMessage(\"An error occurred during email confirmation.\");\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Confirmation Failed\", {\n                    description: \"Please try again or contact support.\"\n                });\n            }\n        };\n        // Start the confirmation process immediately\n        handleEmailConfirmation();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-edubridge-50 to-white flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-md w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-8 text-center\",\n                children: [\n                    status === \"loading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-16 w-16 text-edubridge-600 animate-spin mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirming Your Email\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"Please wait while we verify your email address...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.reload(),\n                                className: \"px-4 py-2 text-sm text-edubridge-600 hover:text-edubridge-800 underline\",\n                                children: \"Taking too long? Click to retry\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-16 w-16 text-green-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Email Confirmed!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Redirecting you to complete your profile...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-red-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Confirmation Failed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signin\"),\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors\",\n                                        children: \"Try Signing In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === \"expired\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-orange-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Link Expired\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"resend-email\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Enter your email to resend confirmation:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                id: \"resend-email\",\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                placeholder: \"<EMAIL>\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-edubridge-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleResendConfirmation,\n                                        disabled: isResending || !email,\n                                        className: \"w-full bg-edubridge-600 text-white py-2 px-4 rounded-md hover:bg-edubridge-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isResending ? \"Sending...\" : \"Resend Confirmation Email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/auth/signup\"),\n                                        className: \"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                                        children: \"Sign Up Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n                lineNumber: 291,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n            lineNumber: 285,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/confirm/page.tsx\",\n        lineNumber: 284,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfirmPage, \"SZaeJxz4oTkosS3Rij4vpgs5NYA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ConfirmPage;\nvar _c;\n$RefreshReg$(_c, \"ConfirmPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/auth/confirm/page.tsx\n"));

/***/ })

});