"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signin/page",{

/***/ "(app-pages-browser)/./app/auth/signin/page.tsx":
/*!**********************************!*\
  !*** ./app/auth/signin/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SignInPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SignInPage() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Check if user is already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.auth.getSession();\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                console.log(\"User already authenticated, redirecting...\");\n                // Get user profile to determine role\n                const { data: profile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"profiles\").select(\"role\").eq(\"id\", session.user.id).single();\n                // Redirect based on role\n                if ((profile === null || profile === void 0 ? void 0 : profile.role) === \"admin\") {\n                    router.push(\"/admin/dashboard\");\n                } else if ((profile === null || profile === void 0 ? void 0 : profile.role) === \"teacher\") {\n                    // Check if teacher has completed profile setup\n                    const { data: teacherProfile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"teacher_profiles\").select(\"id\").eq(\"user_id\", session.user.id).single();\n                    if (teacherProfile) {\n                        router.push(\"/teacher/dashboard\");\n                    } else {\n                        router.push(\"/teacher/setup-profile\");\n                    }\n                } else {\n                    // Check if student has completed profile setup\n                    const { data: studentProfile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"student_profiles\").select(\"id\").eq(\"user_id\", session.user.id).single();\n                    if (studentProfile) {\n                        router.push(\"/student/dashboard\");\n                    } else {\n                        router.push(\"/student/setup-profile\");\n                    }\n                }\n            }\n        };\n        checkAuth();\n    }, []);\n    const handleSignIn = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Sign In Failed\", {\n                    description: error.message\n                });\n                return;\n            }\n            if (data.user) {\n                // Get user profile to determine role\n                const { data: profile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"profiles\").select(\"role\").eq(\"id\", data.user.id).single();\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Welcome back!\", {\n                    description: \"You have been signed in successfully.\"\n                });\n                // Redirect based on role\n                const redirectTo = new URLSearchParams(window.location.search).get(\"redirectTo\");\n                if (redirectTo) {\n                    router.push(redirectTo);\n                } else if ((profile === null || profile === void 0 ? void 0 : profile.role) === \"admin\") {\n                    router.push(\"/admin/dashboard\");\n                } else if ((profile === null || profile === void 0 ? void 0 : profile.role) === \"teacher\") {\n                    // Check if teacher has completed profile setup\n                    const { data: teacherProfile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"teacher_profiles\").select(\"id\").eq(\"user_id\", data.user.id).single();\n                    if (teacherProfile) {\n                        router.push(\"/teacher/dashboard\");\n                    } else {\n                        router.push(\"/teacher/setup-profile\");\n                    }\n                } else {\n                    // Check if student has completed profile setup\n                    const { data: studentProfile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"student_profiles\").select(\"id\").eq(\"user_id\", data.user.id).single();\n                    if (studentProfile) {\n                        router.push(\"/student/dashboard\");\n                    } else {\n                        router.push(\"/student/setup-profile\");\n                    }\n                }\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"An unexpected error occurred\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-edubridge-50 via-white to-edubridge-100 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"inline-flex items-center space-x-2 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-edubridge-500 to-edubridge-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-6 w-6 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl font-bold bg-gradient-to-r from-edubridge-600 to-edubridge-800 bg-clip-text text-transparent\",\n                                children: \"EduBridge\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-2xl\",\n                                    children: \"Welcome Back\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                    children: \"Sign in to your EduBridge account\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSignIn,\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"email\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"email\",\n                                                    type: \"email\",\n                                                    placeholder: \"Enter your email\",\n                                                    value: email,\n                                                    onChange: (e)=>setEmail(e.target.value),\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"password\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"password\",\n                                                            type: showPassword ? \"text\" : \"password\",\n                                                            placeholder: \"Enter your password\",\n                                                            value: password,\n                                                            onChange: (e)=>setPassword(e.target.value),\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full\",\n                                            variant: \"gradient\",\n                                            disabled: isLoading,\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Signing In...\"\n                                                ]\n                                            }, void 0, true) : \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/forgot-password\",\n                                        className: \"text-edubridge-600 hover:text-edubridge-700 hover:underline\",\n                                        children: \"Forgot your password?\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center text-sm text-muted-foreground\",\n                                    children: [\n                                        \"Don't have an account?\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/signup\",\n                                            className: \"text-edubridge-600 hover:text-edubridge-700 hover:underline font-medium\",\n                                            children: \"Sign up\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(SignInPage, \"O8XIdgaSS2fMqAc2TT7I8Cv6op8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SignInPage;\nvar _c;\n$RefreshReg$(_c, \"SignInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/auth/signin/page.tsx\n"));

/***/ })

});