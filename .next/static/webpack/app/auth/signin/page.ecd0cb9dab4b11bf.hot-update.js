"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signin/page",{

/***/ "(app-pages-browser)/./app/auth/signin/page.tsx":
/*!**********************************!*\
  !*** ./app/auth/signin/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SignInPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,EyeOff,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SignInPage() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Check if user is already authenticated\n    useEffect(()=>{\n        const checkAuth = async ()=>{\n            const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.auth.getSession();\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                console.log(\"User already authenticated, redirecting...\");\n                // Get user profile to determine role\n                const { data: profile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"profiles\").select(\"role\").eq(\"id\", session.user.id).single();\n                // Redirect based on role\n                if ((profile === null || profile === void 0 ? void 0 : profile.role) === \"admin\") {\n                    router.push(\"/admin/dashboard\");\n                } else if ((profile === null || profile === void 0 ? void 0 : profile.role) === \"teacher\") {\n                    // Check if teacher has completed profile setup\n                    const { data: teacherProfile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"teacher_profiles\").select(\"id\").eq(\"user_id\", session.user.id).single();\n                    if (teacherProfile) {\n                        router.push(\"/teacher/dashboard\");\n                    } else {\n                        router.push(\"/teacher/setup-profile\");\n                    }\n                } else {\n                    // Check if student has completed profile setup\n                    const { data: studentProfile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"student_profiles\").select(\"id\").eq(\"user_id\", session.user.id).single();\n                    if (studentProfile) {\n                        router.push(\"/student/dashboard\");\n                    } else {\n                        router.push(\"/student/setup-profile\");\n                    }\n                }\n            }\n        };\n        checkAuth();\n    }, []);\n    const handleSignIn = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Sign In Failed\", {\n                    description: error.message\n                });\n                return;\n            }\n            if (data.user) {\n                // Get user profile to determine role\n                const { data: profile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"profiles\").select(\"role\").eq(\"id\", data.user.id).single();\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Welcome back!\", {\n                    description: \"You have been signed in successfully.\"\n                });\n                // Redirect based on role\n                const redirectTo = new URLSearchParams(window.location.search).get(\"redirectTo\");\n                if (redirectTo) {\n                    router.push(redirectTo);\n                } else if ((profile === null || profile === void 0 ? void 0 : profile.role) === \"admin\") {\n                    router.push(\"/admin/dashboard\");\n                } else if ((profile === null || profile === void 0 ? void 0 : profile.role) === \"teacher\") {\n                    // Check if teacher has completed profile setup\n                    const { data: teacherProfile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"teacher_profiles\").select(\"id\").eq(\"user_id\", data.user.id).single();\n                    if (teacherProfile) {\n                        router.push(\"/teacher/dashboard\");\n                    } else {\n                        router.push(\"/teacher/setup-profile\");\n                    }\n                } else {\n                    // Check if student has completed profile setup\n                    const { data: studentProfile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"student_profiles\").select(\"id\").eq(\"user_id\", data.user.id).single();\n                    if (studentProfile) {\n                        router.push(\"/student/dashboard\");\n                    } else {\n                        router.push(\"/student/setup-profile\");\n                    }\n                }\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"An unexpected error occurred\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-edubridge-50 via-white to-edubridge-100 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"inline-flex items-center space-x-2 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-edubridge-500 to-edubridge-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-6 w-6 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl font-bold bg-gradient-to-r from-edubridge-600 to-edubridge-800 bg-clip-text text-transparent\",\n                                children: \"EduBridge\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-2xl\",\n                                    children: \"Welcome Back\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                    children: \"Sign in to your EduBridge account\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSignIn,\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"email\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"email\",\n                                                    type: \"email\",\n                                                    placeholder: \"Enter your email\",\n                                                    value: email,\n                                                    onChange: (e)=>setEmail(e.target.value),\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"password\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"password\",\n                                                            type: showPassword ? \"text\" : \"password\",\n                                                            placeholder: \"Enter your password\",\n                                                            value: password,\n                                                            onChange: (e)=>setPassword(e.target.value),\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full\",\n                                            variant: \"gradient\",\n                                            disabled: isLoading,\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Signing In...\"\n                                                ]\n                                            }, void 0, true) : \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/forgot-password\",\n                                        className: \"text-edubridge-600 hover:text-edubridge-700 hover:underline\",\n                                        children: \"Forgot your password?\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center text-sm text-muted-foreground\",\n                                    children: [\n                                        \"Don't have an account?\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/signup\",\n                                            className: \"text-edubridge-600 hover:text-edubridge-700 hover:underline font-medium\",\n                                            children: \"Sign up\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Edu-bridge/app/auth/signin/page.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(SignInPage, \"O8XIdgaSS2fMqAc2TT7I8Cv6op8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SignInPage;\nvar _c;\n$RefreshReg$(_c, \"SignInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/auth/signin/page.tsx\n"));

/***/ })

});