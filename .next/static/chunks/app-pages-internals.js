/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app-pages-internals"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lMkZVc2VycyUyRnRhaGFmYXJvb3F1aSUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZFZHUtYnJpZGdlJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGYXBwLXJvdXRlci5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkVkdS1icmlkZ2UlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkVkdS1icmlkZ2UlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZsYXlvdXQtcm91dGVyLmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZ0YWhhZmFyb29xdWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGRWR1LWJyaWRnZSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm5vdC1mb3VuZC1ib3VuZGFyeS5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkVkdS1icmlkZ2UlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZ0YWhhZmFyb29xdWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGRWR1LWJyaWRnZSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRnN0YXRpYy1nZW5lcmF0aW9uLXNlYXJjaHBhcmFtcy1iYWlsb3V0LXByb3ZpZGVyLmpzJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ1BBQXFKO0FBQ3JKLHdQQUF5SjtBQUN6SixzUEFBd0o7QUFDeEosZ1FBQTZKO0FBQzdKLG9SQUF1SztBQUN2SyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2QzNzYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0VkdS1icmlkZ2Uvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9hcHAtcm91dGVyLmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0VkdS1icmlkZ2Uvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9FZHUtYnJpZGdlL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9FZHUtYnJpZGdlL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0VkdS1icmlkZ2Uvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9yZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0VkdS1icmlkZ2Uvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9zdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/hooks-server-context.js ***!
  \**************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DYNAMIC_ERROR_CODE: function() {\n        return DYNAMIC_ERROR_CODE;\n    },\n    DynamicServerError: function() {\n        return DynamicServerError;\n    }\n});\nconst DYNAMIC_ERROR_CODE = \"DYNAMIC_SERVER_USAGE\";\nclass DynamicServerError extends Error {\n    constructor(type){\n        super(\"Dynamic server usage: \" + type);\n        this.digest = DYNAMIC_ERROR_CODE;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hooks-server-context.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/components/layout-router.js ***!
  \*******************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return OuterLayoutRouter;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _fetchserverresponse = __webpack_require__(/*! ./router-reducer/fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _infinitepromise = __webpack_require__(/*! ./infinite-promise */ \"(app-pages-browser)/./node_modules/next/dist/client/components/infinite-promise.js\");\nconst _errorboundary = __webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nconst _matchsegments = __webpack_require__(/*! ./match-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ../../shared/lib/router/utils/handle-smooth-scroll */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _notfoundboundary = __webpack_require__(/*! ./not-found-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js\");\nconst _getsegmentvalue = __webpack_require__(/*! ./router-reducer/reducers/get-segment-value */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js\");\nconst _createroutercachekey = __webpack_require__(/*! ./router-reducer/create-router-cache-key */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js\");\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */ function walkAddRefetch(segmentPathToWalk, treeToRecreate) {\n    if (segmentPathToWalk) {\n        const [segment, parallelRouteKey] = segmentPathToWalk;\n        const isLast = segmentPathToWalk.length === 2;\n        if ((0, _matchsegments.matchSegment)(treeToRecreate[0], segment)) {\n            if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n                if (isLast) {\n                    const subTree = walkAddRefetch(undefined, treeToRecreate[1][parallelRouteKey]);\n                    return [\n                        treeToRecreate[0],\n                        {\n                            ...treeToRecreate[1],\n                            [parallelRouteKey]: [\n                                subTree[0],\n                                subTree[1],\n                                subTree[2],\n                                \"refetch\"\n                            ]\n                        }\n                    ];\n                }\n                return [\n                    treeToRecreate[0],\n                    {\n                        ...treeToRecreate[1],\n                        [parallelRouteKey]: walkAddRefetch(segmentPathToWalk.slice(2), treeToRecreate[1][parallelRouteKey])\n                    }\n                ];\n            }\n        }\n    }\n    return treeToRecreate;\n}\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */ function findDOMNode(instance) {\n    // Tree-shake for server bundle\n    if (false) {}\n    // Only apply strict mode warning when not in production\n    if (true) {\n        const originalConsoleError = console.error;\n        try {\n            console.error = function() {\n                for(var _len = arguments.length, messages = new Array(_len), _key = 0; _key < _len; _key++){\n                    messages[_key] = arguments[_key];\n                }\n                // Ignore strict mode warning for the findDomNode call below\n                if (!messages[0].includes(\"Warning: %s is deprecated in StrictMode.\")) {\n                    originalConsoleError(...messages);\n                }\n            };\n            return _reactdom.default.findDOMNode(instance);\n        } finally{\n            console.error = originalConsoleError;\n        }\n    }\n    return _reactdom.default.findDOMNode(instance);\n}\nconst rectProperties = [\n    \"bottom\",\n    \"height\",\n    \"left\",\n    \"right\",\n    \"top\",\n    \"width\",\n    \"x\",\n    \"y\"\n];\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */ function shouldSkipElement(element) {\n    // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n    // and will result in a situation we bail on scroll because of something like a fixed nav,\n    // even though the actual page content is offscreen\n    if ([\n        \"sticky\",\n        \"fixed\"\n    ].includes(getComputedStyle(element).position)) {\n        if (true) {\n            console.warn(\"Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:\", element);\n        }\n        return true;\n    }\n    // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n    // because `offsetParent` doesn't consider document/body\n    const rect = element.getBoundingClientRect();\n    return rectProperties.every((item)=>rect[item] === 0);\n}\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */ function topOfElementInViewport(element, viewportHeight) {\n    const rect = element.getBoundingClientRect();\n    return rect.top >= 0 && rect.top <= viewportHeight;\n}\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */ function getHashFragmentDomNode(hashFragment) {\n    // If the hash fragment is `top` the page has to scroll to the top of the page.\n    if (hashFragment === \"top\") {\n        return document.body;\n    }\n    var _document_getElementById;\n    // If the hash fragment is an id, the page has to scroll to the element with that id.\n    return (_document_getElementById = document.getElementById(hashFragment)) != null ? _document_getElementById : document.getElementsByName(hashFragment)[0];\n}\nclass InnerScrollAndFocusHandler extends _react.default.Component {\n    componentDidMount() {\n        this.handlePotentialScroll();\n    }\n    componentDidUpdate() {\n        // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n        if (this.props.focusAndScrollRef.apply) {\n            this.handlePotentialScroll();\n        }\n    }\n    render() {\n        return this.props.children;\n    }\n    constructor(...args){\n        super(...args);\n        this.handlePotentialScroll = ()=>{\n            // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n            const { focusAndScrollRef, segmentPath } = this.props;\n            if (focusAndScrollRef.apply) {\n                // segmentPaths is an array of segment paths that should be scrolled to\n                // if the current segment path is not in the array, the scroll is not applied\n                // unless the array is empty, in which case the scroll is always applied\n                if (focusAndScrollRef.segmentPaths.length !== 0 && !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath)=>segmentPath.every((segment, index)=>(0, _matchsegments.matchSegment)(segment, scrollRefSegmentPath[index])))) {\n                    return;\n                }\n                let domNode = null;\n                const hashFragment = focusAndScrollRef.hashFragment;\n                if (hashFragment) {\n                    domNode = getHashFragmentDomNode(hashFragment);\n                }\n                // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n                // This already caused a bug where the first child was a <link/> in head.\n                if (!domNode) {\n                    domNode = findDOMNode(this);\n                }\n                // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n                if (!(domNode instanceof Element)) {\n                    return;\n                }\n                // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n                // If the element is skipped, try to select the next sibling and try again.\n                while(!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)){\n                    // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n                    if (domNode.nextElementSibling === null) {\n                        return;\n                    }\n                    domNode = domNode.nextElementSibling;\n                }\n                // State is mutated to ensure that the focus and scroll is applied only once.\n                focusAndScrollRef.apply = false;\n                focusAndScrollRef.hashFragment = null;\n                focusAndScrollRef.segmentPaths = [];\n                (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n                    // In case of hash scroll, we only need to scroll the element into view\n                    if (hashFragment) {\n                        domNode.scrollIntoView();\n                        return;\n                    }\n                    // Store the current viewport height because reading `clientHeight` causes a reflow,\n                    // and it won't change during this function.\n                    const htmlElement = document.documentElement;\n                    const viewportHeight = htmlElement.clientHeight;\n                    // If the element's top edge is already in the viewport, exit early.\n                    if (topOfElementInViewport(domNode, viewportHeight)) {\n                        return;\n                    }\n                    // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n                    // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n                    // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n                    // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n                    htmlElement.scrollTop = 0;\n                    // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n                    if (!topOfElementInViewport(domNode, viewportHeight)) {\n                        domNode.scrollIntoView();\n                    }\n                }, {\n                    // We will force layout by querying domNode position\n                    dontForceLayout: true,\n                    onlyHashChange: focusAndScrollRef.onlyHashChange\n                });\n                // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n                focusAndScrollRef.onlyHashChange = false;\n                // Set focus on the element\n                domNode.focus();\n            }\n        };\n    }\n}\nfunction ScrollAndFocusHandler(param) {\n    let { segmentPath, children } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw new Error(\"invariant global layout router not mounted\");\n    }\n    return /*#__PURE__*/ _react.default.createElement(InnerScrollAndFocusHandler, {\n        segmentPath: segmentPath,\n        focusAndScrollRef: context.focusAndScrollRef\n    }, children);\n}\n_c = ScrollAndFocusHandler;\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */ function InnerLayoutRouter(param) {\n    let { parallelRouterKey, url, childNodes, segmentPath, tree, // isActive,\n    cacheKey } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw new Error(\"invariant global layout router not mounted\");\n    }\n    const { buildId, changeByServerResponse, tree: fullTree } = context;\n    // Read segment path from the parallel router cache node.\n    let childNode = childNodes.get(cacheKey);\n    // When childNode is not available during rendering client-side we need to fetch it from the server.\n    if (!childNode || childNode.status === _approutercontextsharedruntime.CacheStates.LAZY_INITIALIZED) {\n        /**\n     * Router state with refetch marker added\n     */ // TODO-APP: remove ''\n        const refetchTree = walkAddRefetch([\n            \"\",\n            ...segmentPath\n        ], fullTree);\n        childNode = {\n            status: _approutercontextsharedruntime.CacheStates.DATA_FETCH,\n            data: (0, _fetchserverresponse.fetchServerResponse)(new URL(url, location.origin), refetchTree, context.nextUrl, buildId),\n            subTreeData: null,\n            head: childNode && childNode.status === _approutercontextsharedruntime.CacheStates.LAZY_INITIALIZED ? childNode.head : undefined,\n            parallelRoutes: childNode && childNode.status === _approutercontextsharedruntime.CacheStates.LAZY_INITIALIZED ? childNode.parallelRoutes : new Map()\n        };\n        /**\n     * Flight data fetch kicked off during render and put into the cache.\n     */ childNodes.set(cacheKey, childNode);\n    }\n    // This case should never happen so it throws an error. It indicates there's a bug in the Next.js.\n    if (!childNode) {\n        throw new Error(\"Child node should always exist\");\n    }\n    // This case should never happen so it throws an error. It indicates there's a bug in the Next.js.\n    if (childNode.subTreeData && childNode.data) {\n        throw new Error(\"Child node should not have both subTreeData and data\");\n    }\n    // If cache node has a data request we have to unwrap response by `use` and update the cache.\n    if (childNode.data) {\n        /**\n     * Flight response data\n     */ // When the data has not resolved yet `use` will suspend here.\n        const [flightData, overrideCanonicalUrl] = (0, _react.use)(childNode.data);\n        // segmentPath from the server does not match the layout's segmentPath\n        childNode.data = null;\n        // setTimeout is used to start a new transition during render, this is an intentional hack around React.\n        setTimeout(()=>{\n            (0, _react.startTransition)(()=>{\n                changeByServerResponse(fullTree, flightData, overrideCanonicalUrl);\n            });\n        });\n        // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n        (0, _react.use)((0, _infinitepromise.createInfinitePromise)());\n    }\n    // If cache node has no subTreeData and no data request we have to infinitely suspend as the data will likely flow in from another place.\n    // TODO-APP: double check users can't return null in a component that will kick in here.\n    if (!childNode.subTreeData) {\n        (0, _react.use)((0, _infinitepromise.createInfinitePromise)());\n    }\n    const subtree = /*#__PURE__*/ _react.default.createElement(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n        value: {\n            tree: tree[1][parallelRouterKey],\n            childNodes: childNode.parallelRoutes,\n            // TODO-APP: overriding of url for parallel routes\n            url: url\n        }\n    }, childNode.subTreeData);\n    // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n    return subtree;\n}\n_c1 = InnerLayoutRouter;\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */ function LoadingBoundary(param) {\n    let { children, loading, loadingStyles, loadingScripts, hasLoading } = param;\n    if (hasLoading) {\n        return /*#__PURE__*/ _react.default.createElement(_react.Suspense, {\n            fallback: /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, loadingStyles, loadingScripts, loading)\n        }, children);\n    }\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, children);\n}\n_c2 = LoadingBoundary;\nfunction OuterLayoutRouter(param) {\n    let { parallelRouterKey, segmentPath, error, errorStyles, errorScripts, templateStyles, templateScripts, loading, loadingStyles, loadingScripts, hasLoading, template, notFound, notFoundStyles, styles } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.LayoutRouterContext);\n    if (!context) {\n        throw new Error(\"invariant expected layout router to be mounted\");\n    }\n    const { childNodes, tree, url } = context;\n    // Get the current parallelRouter cache node\n    let childNodesForParallelRouter = childNodes.get(parallelRouterKey);\n    // If the parallel router cache node does not exist yet, create it.\n    // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n    if (!childNodesForParallelRouter) {\n        childNodesForParallelRouter = new Map();\n        childNodes.set(parallelRouterKey, childNodesForParallelRouter);\n    }\n    // Get the active segment in the tree\n    // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n    const treeSegment = tree[1][parallelRouterKey][0];\n    // If segment is an array it's a dynamic route and we want to read the dynamic route value as the segment to get from the cache.\n    const currentChildSegmentValue = (0, _getsegmentvalue.getSegmentValue)(treeSegment);\n    /**\n   * Decides which segments to keep rendering, all segments that are not active will be wrapped in `<Offscreen>`.\n   */ // TODO-APP: Add handling of `<Offscreen>` when it's available.\n    const preservedSegments = [\n        treeSegment\n    ];\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, styles, preservedSegments.map((preservedSegment)=>{\n        const preservedSegmentValue = (0, _getsegmentvalue.getSegmentValue)(preservedSegment);\n        const cacheKey = (0, _createroutercachekey.createRouterCacheKey)(preservedSegment);\n        return(/*\n            - Error boundary\n              - Only renders error boundary if error component is provided.\n              - Rendered for each segment to ensure they have their own error state.\n            - Loading boundary\n              - Only renders suspense boundary if loading components is provided.\n              - Rendered for each segment to ensure they have their own loading state.\n              - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n          */ /*#__PURE__*/ _react.default.createElement(_approutercontextsharedruntime.TemplateContext.Provider, {\n            key: (0, _createroutercachekey.createRouterCacheKey)(preservedSegment, true),\n            value: /*#__PURE__*/ _react.default.createElement(ScrollAndFocusHandler, {\n                segmentPath: segmentPath\n            }, /*#__PURE__*/ _react.default.createElement(_errorboundary.ErrorBoundary, {\n                errorComponent: error,\n                errorStyles: errorStyles,\n                errorScripts: errorScripts\n            }, /*#__PURE__*/ _react.default.createElement(LoadingBoundary, {\n                hasLoading: hasLoading,\n                loading: loading,\n                loadingStyles: loadingStyles,\n                loadingScripts: loadingScripts\n            }, /*#__PURE__*/ _react.default.createElement(_notfoundboundary.NotFoundBoundary, {\n                notFound: notFound,\n                notFoundStyles: notFoundStyles\n            }, /*#__PURE__*/ _react.default.createElement(_redirectboundary.RedirectBoundary, null, /*#__PURE__*/ _react.default.createElement(InnerLayoutRouter, {\n                parallelRouterKey: parallelRouterKey,\n                url: url,\n                tree: tree,\n                childNodes: childNodesForParallelRouter,\n                segmentPath: segmentPath,\n                cacheKey: cacheKey,\n                isActive: currentChildSegmentValue === preservedSegmentValue\n            }))))))\n        }, templateStyles, templateScripts, template));\n    }));\n}\n_c3 = OuterLayoutRouter;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=layout-router.js.map\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScrollAndFocusHandler\");\n$RefreshReg$(_c1, \"InnerLayoutRouter\");\n$RefreshReg$(_c2, \"LoadingBoundary\");\n$RefreshReg$(_c3, \"OuterLayoutRouter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/render-from-template-context.js ***!
  \**********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return RenderFromTemplateContext;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nfunction RenderFromTemplateContext() {\n    const children = (0, _react.useContext)(_approutercontextsharedruntime.TemplateContext);\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, children);\n}\n_c = RenderFromTemplateContext;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=render-from-template-context.js.map\nvar _c;\n$RefreshReg$(_c, \"RenderFromTemplateContext\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/searchparams-bailout-proxy.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/searchparams-bailout-proxy.js ***!
  \********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createSearchParamsBailoutProxy\", ({\n    enumerable: true,\n    get: function() {\n        return createSearchParamsBailoutProxy;\n    }\n}));\nconst _staticgenerationbailout = __webpack_require__(/*! ./static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nfunction createSearchParamsBailoutProxy() {\n    return new Proxy({}, {\n        get (_target, prop) {\n            // React adds some properties on the object when serializing for client components\n            if (typeof prop === \"string\") {\n                (0, _staticgenerationbailout.staticGenerationBailout)(\"searchParams.\" + prop);\n            }\n        }\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=searchparams-bailout-proxy.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/searchparams-bailout-proxy.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-async-storage.external.js ***!
  \**********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"staticGenerationAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return staticGenerationAsyncStorage;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(shared)/./node_modules/next/dist/client/components/async-local-storage.js\");\nconst staticGenerationAsyncStorage = (0, _asynclocalstorage.createAsyncLocalStorage)();\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-async-storage.external.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-bailout.js ***!
  \*******************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"staticGenerationBailout\", ({\n    enumerable: true,\n    get: function() {\n        return staticGenerationBailout;\n    }\n}));\nconst _hooksservercontext = __webpack_require__(/*! ./hooks-server-context */ \"(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js\");\nconst _staticgenerationasyncstorageexternal = __webpack_require__(/*! ./static-generation-async-storage.external */ \"(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js\");\nclass StaticGenBailoutError extends Error {\n    constructor(...args){\n        super(...args);\n        this.code = \"NEXT_STATIC_GEN_BAILOUT\";\n    }\n}\nfunction formatErrorMessage(reason, opts) {\n    const { dynamic, link } = opts || {};\n    const suffix = link ? \" See more info here: \" + link : \"\";\n    return \"Page\" + (dynamic ? ' with `dynamic = \"' + dynamic + '\"`' : \"\") + \" couldn't be rendered statically because it used `\" + reason + \"`.\" + suffix;\n}\nconst staticGenerationBailout = (reason, param)=>{\n    let { dynamic, link } = param === void 0 ? {} : param;\n    const staticGenerationStore = _staticgenerationasyncstorageexternal.staticGenerationAsyncStorage.getStore();\n    if (!staticGenerationStore) return false;\n    if (staticGenerationStore.forceStatic) {\n        return true;\n    }\n    if (staticGenerationStore.dynamicShouldError) {\n        throw new StaticGenBailoutError(formatErrorMessage(reason, {\n            link,\n            dynamic: dynamic != null ? dynamic : \"error\"\n        }));\n    }\n    const message = formatErrorMessage(reason, {\n        dynamic,\n        // this error should be caught by Next to bail out of static generation\n        // in case it's uncaught, this link provides some additional context as to why\n        link: \"https://nextjs.org/docs/messages/dynamic-server-error\"\n    });\n    // If postpone is available, we should postpone the render.\n    staticGenerationStore.postpone == null ? void 0 : staticGenerationStore.postpone.call(staticGenerationStore, reason);\n    // As this is a bailout, we don't want to revalidate, so set the revalidate\n    // to 0.\n    staticGenerationStore.revalidate = 0;\n    if (staticGenerationStore.isStaticGeneration) {\n        const err = new _hooksservercontext.DynamicServerError(message);\n        staticGenerationStore.dynamicUsageDescription = reason;\n        staticGenerationStore.dynamicUsageStack = err.stack;\n        throw err;\n    }\n    return false;\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-bailout.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js ***!
  \*****************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return StaticGenerationSearchParamsBailoutProvider;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _searchparamsbailoutproxy = __webpack_require__(/*! ./searchparams-bailout-proxy */ \"(app-pages-browser)/./node_modules/next/dist/client/components/searchparams-bailout-proxy.js\");\nfunction StaticGenerationSearchParamsBailoutProvider(param) {\n    let { Component, propsForComponent, isStaticGeneration } = param;\n    if (isStaticGeneration) {\n        const searchParams = (0, _searchparamsbailoutproxy.createSearchParamsBailoutProxy)();\n        return /*#__PURE__*/ _react.default.createElement(Component, {\n            searchParams: searchParams,\n            ...propsForComponent\n        });\n    }\n    return /*#__PURE__*/ _react.default.createElement(Component, propsForComponent);\n}\n_c = StaticGenerationSearchParamsBailoutProvider;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-searchparams-bailout-provider.js.map\nvar _c;\n$RefreshReg$(_c, \"StaticGenerationSearchParamsBailoutProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js ***!
  \********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"handleSmoothScroll\", ({\n    enumerable: true,\n    get: function() {\n        return handleSmoothScroll;\n    }\n}));\nfunction handleSmoothScroll(fn, options) {\n    if (options === void 0) options = {};\n    // if only the hash is changed, we don't need to disable smooth scrolling\n    // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n    if (options.onlyHashChange) {\n        fn();\n        return;\n    }\n    const htmlElement = document.documentElement;\n    const existing = htmlElement.style.scrollBehavior;\n    htmlElement.style.scrollBehavior = \"auto\";\n    if (!options.dontForceLayout) {\n        // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n        // Otherwise it will not pickup the change in scrollBehavior\n        // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n        htmlElement.getClientRects();\n    }\n    fn();\n    htmlElement.style.scrollBehavior = existing;\n} //# sourceMappingURL=handle-smooth-scroll.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FEdu-bridge%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);