{"name": "@supabase/auth-helpers-shared", "version": "0.7.0", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "publishConfig": {"access": "public"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/supabase/auth-helpers.git"}, "keywords": ["Supabase", "<PERSON><PERSON>", "Svelte Kit", "Svelte"], "author": "Supabase", "license": "MIT", "bugs": {"url": "https://github.com/supabase/auth-helpers/issues"}, "homepage": "https://github.com/supabase/auth-helpers#readme", "devDependencies": {"@supabase/supabase-js": "2.42.0", "@types/cookie": "^0.5.1", "cookie": "^0.5.0", "next": "^13.5.5", "react": ">=17.0.2 <18.0.0 || >=18.0.0-0 <19.0.0", "react-dom": "^17.0.2 || ^18.0.0-0", "tsup": "^6.7.0", "vitest": "^0.34.6", "tsconfig": "0.1.1"}, "dependencies": {"jose": "^4.14.4"}, "peerDependencies": {"@supabase/supabase-js": "^2.39.8"}, "scripts": {"lint": "tsc", "build": "tsup", "test": "vitest run", "test:watch": "vitest"}}