-- EduBridge Database Schema - Simplified for New Authentication Flow
-- Run this in your Supabase SQL editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('student', 'teacher', 'admin');
CREATE TYPE board_type AS ENUM ('punjab', 'sindh', 'kpk', 'federal', 'balochistan');
CREATE TYPE province_type AS ENUM ('punjab', 'sindh', 'kpk', 'balochistan', 'islamabad');
CREATE TYPE subscription_status AS ENUM ('active', 'cancelled', 'expired');
CREATE TYPE payment_status AS ENUM ('completed', 'pending', 'failed');

-- Drop existing tables if they exist (be careful in production!)
DROP TABLE IF EXISTS course_purchases CASCADE;
DROP TABLE IF EXISTS subscriptions CASCADE;
DROP TABLE IF EXISTS courses CASCADE;
DROP TABLE IF EXISTS teachers CASCADE;
DROP TABLE IF EXISTS students CASCADE;
DROP TABLE IF EXISTS profiles CASCADE;

-- Profiles table (extends auth.users)
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    role user_role NOT NULL DEFAULT 'student',
    phone TEXT,
    date_of_birth DATE,
    city TEXT,
    province province_type,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Students table
CREATE TABLE students (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
    class_level INTEGER NOT NULL CHECK (class_level >= 5 AND class_level <= 12),
    board board_type NOT NULL,
    subjects TEXT[] DEFAULT '{}',
    parent_name TEXT,
    parent_phone TEXT,
    parent_email TEXT,
    learning_goals TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Teachers table
CREATE TABLE teachers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
    qualifications TEXT[] DEFAULT '{}',
    experience_years INTEGER DEFAULT 0,
    subjects TEXT[] DEFAULT '{}',
    class_levels INTEGER[] DEFAULT '{}',
    bio TEXT,
    hourly_rate DECIMAL(10,2) DEFAULT 0,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_documents TEXT[] DEFAULT '{}',
    rating DECIMAL(3,2) DEFAULT 0.0,
    total_reviews INTEGER DEFAULT 0,
    total_students INTEGER DEFAULT 0,
    total_earnings DECIMAL(12,2) DEFAULT 0.0,
    bank_account TEXT,
    jazzcash_account TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Courses table
CREATE TABLE courses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    teacher_id UUID REFERENCES teachers(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    subject TEXT NOT NULL,
    class_level INTEGER NOT NULL CHECK (class_level >= 5 AND class_level <= 12),
    board board_type NOT NULL,
    price DECIMAL(10,2) NOT NULL DEFAULT 0,
    currency TEXT DEFAULT 'PKR',
    duration_hours INTEGER NOT NULL,
    thumbnail_url TEXT,
    is_published BOOLEAN DEFAULT FALSE,
    total_enrollments INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.0,
    total_reviews INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscriptions table (for teacher subscriptions)
CREATE TABLE subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    student_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    teacher_id UUID REFERENCES teachers(id) ON DELETE CASCADE NOT NULL,
    plan_type TEXT NOT NULL CHECK (plan_type IN ('monthly', 'quarterly', 'yearly')),
    status subscription_status DEFAULT 'active',
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'PKR',
    payment_method TEXT DEFAULT 'mock_payment',
    transaction_id TEXT NOT NULL,
    starts_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, teacher_id, status) -- Prevent multiple active subscriptions
);

-- Course purchases table
CREATE TABLE course_purchases (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    student_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'PKR',
    payment_method TEXT DEFAULT 'mock_payment',
    transaction_id TEXT NOT NULL,
    status payment_status DEFAULT 'completed',
    purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, course_id) -- Prevent duplicate purchases
);

-- Create indexes for better performance
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_students_profile_id ON students(profile_id);
CREATE INDEX idx_students_class_board ON students(class_level, board);
CREATE INDEX idx_teachers_profile_id ON teachers(profile_id);
CREATE INDEX idx_teachers_verified ON teachers(is_verified);
CREATE INDEX idx_teachers_subjects ON teachers USING GIN(subjects);
CREATE INDEX idx_courses_teacher_id ON courses(teacher_id);
CREATE INDEX idx_courses_published ON courses(is_published);
CREATE INDEX idx_courses_subject_class ON courses(subject, class_level);
CREATE INDEX idx_subscriptions_student_teacher ON subscriptions(student_id, teacher_id);
CREATE INDEX idx_subscriptions_status_expires ON subscriptions(status, expires_at);
CREATE INDEX idx_course_purchases_student_course ON course_purchases(student_id, course_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON students
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teachers_updated_at BEFORE UPDATE ON teachers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_purchases ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Profiles: Users can read all profiles, but only update their own
CREATE POLICY "Public profiles are viewable by everyone" ON profiles
    FOR SELECT USING (true);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Students: Can read all, but only manage their own
CREATE POLICY "Students are viewable by everyone" ON students
    FOR SELECT USING (true);

CREATE POLICY "Users can manage own student profile" ON students
    FOR ALL USING (auth.uid() = profile_id);

-- Teachers: Can read all, but only manage their own
CREATE POLICY "Teachers are viewable by everyone" ON teachers
    FOR SELECT USING (true);

CREATE POLICY "Users can manage own teacher profile" ON teachers
    FOR ALL USING (auth.uid() = profile_id);

-- Courses: Public read, teachers can manage their own
CREATE POLICY "Published courses are viewable by everyone" ON courses
    FOR SELECT USING (is_published = true OR auth.uid() IN (
        SELECT profile_id FROM teachers WHERE id = teacher_id
    ));

CREATE POLICY "Teachers can manage own courses" ON courses
    FOR ALL USING (auth.uid() IN (
        SELECT profile_id FROM teachers WHERE id = teacher_id
    ));

-- Subscriptions: Users can see their own subscriptions
CREATE POLICY "Users can view own subscriptions" ON subscriptions
    FOR SELECT USING (
        auth.uid() = student_id OR 
        auth.uid() IN (SELECT profile_id FROM teachers WHERE id = teacher_id)
    );

CREATE POLICY "Students can create subscriptions" ON subscriptions
    FOR INSERT WITH CHECK (auth.uid() = student_id);

CREATE POLICY "Users can update own subscriptions" ON subscriptions
    FOR UPDATE USING (
        auth.uid() = student_id OR 
        auth.uid() IN (SELECT profile_id FROM teachers WHERE id = teacher_id)
    );

-- Course purchases: Users can see their own purchases
CREATE POLICY "Users can view own course purchases" ON course_purchases
    FOR SELECT USING (
        auth.uid() = student_id OR 
        auth.uid() IN (
            SELECT t.profile_id FROM teachers t 
            JOIN courses c ON c.teacher_id = t.id 
            WHERE c.id = course_id
        )
    );

CREATE POLICY "Students can create course purchases" ON course_purchases
    FOR INSERT WITH CHECK (auth.uid() = student_id);

-- Insert some sample data for testing (optional)
-- Uncomment the following lines if you want sample data

/*
-- Sample subjects for the platform
INSERT INTO profiles (id, email, full_name, role) VALUES
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Dr. Ahmed Khan', 'teacher'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Sara Ali', 'student');

INSERT INTO teachers (profile_id, qualifications, experience_years, subjects, class_levels, bio, hourly_rate, is_verified) VALUES
('550e8400-e29b-41d4-a716-************',
 ARRAY['PhD Mathematics', 'MS Physics'],
 8,
 ARRAY['Mathematics', 'Physics'],
 ARRAY[9, 10, 11, 12],
 'Experienced mathematics and physics teacher specializing in Pakistani curriculum.',
 1500.00,
 true);

INSERT INTO students (profile_id, class_level, board, subjects, parent_name, parent_phone, learning_goals) VALUES
('550e8400-e29b-41d4-a716-************',
 10,
 'federal',
 ARRAY['Mathematics', 'Physics'],
 'Ali Khan',
 '+92-300-1234567',
 'Improve grades in mathematics and physics');
*/

-- Final message
SELECT 'EduBridge database schema setup complete! 🎉' as message;
