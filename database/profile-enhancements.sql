-- EduBridge Profile Enhancement SQL
-- Run these queries in your Supabase SQL editor to enhance student and teacher profiles

-- 1. Add missing fields to profiles table
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS gender TEXT CHECK (gender IN ('male', 'female', 'other'));
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS address TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS emergency_contact TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS emergency_phone TEXT;

-- 2. Enhance students table with more detailed fields
ALTER TABLE students ADD COLUMN IF NOT EXISTS preferred_subjects TEXT[] DEFAULT '{}';
ALTER TABLE students ADD COLUMN IF NOT EXISTS weak_subjects TEXT[] DEFAULT '{}';
ALTER TABLE students ADD COLUMN IF NOT EXISTS study_goals TEXT;
ALTER TABLE students ADD COLUMN IF NOT EXISTS preferred_study_time TEXT CHECK (preferred_study_time IN ('morning', 'afternoon', 'evening', 'night', 'flexible'));
ALTER TABLE students ADD COLUMN IF NOT EXISTS budget_range TEXT CHECK (budget_range IN ('under_1000', '1000_3000', '3000_5000', '5000_10000', 'above_10000'));
ALTER TABLE students ADD COLUMN IF NOT EXISTS learning_style TEXT CHECK (learning_style IN ('visual', 'auditory', 'kinesthetic', 'reading_writing', 'mixed'));

-- 3. Enhance teachers table with comprehensive fields
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS specialization TEXT;
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS teaching_style TEXT CHECK (teaching_style IN ('interactive', 'lecture_based', 'practical', 'discussion', 'mixed'));
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS languages TEXT[] DEFAULT '{}';
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS availability_days TEXT[] DEFAULT '{}';
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS availability_times TEXT[] DEFAULT '{}';
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS min_hourly_rate DECIMAL(10,2) DEFAULT 500.00;
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS max_hourly_rate DECIMAL(10,2) DEFAULT 5000.00;
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS preferred_class_size INTEGER DEFAULT 1;
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS online_teaching BOOLEAN DEFAULT true;
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS home_tutoring BOOLEAN DEFAULT false;
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS institute_teaching BOOLEAN DEFAULT false;
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS cnic TEXT;
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS education_level TEXT CHECK (education_level IN ('bachelors', 'masters', 'phd', 'diploma', 'other'));
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS university TEXT;
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS graduation_year INTEGER;
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS teaching_experience_description TEXT;

-- 4. Create subject categories enum for better organization
CREATE TYPE subject_category AS ENUM (
    'mathematics', 'physics', 'chemistry', 'biology', 'computer_science',
    'english', 'urdu', 'islamiat', 'pakistan_studies', 'social_studies',
    'economics', 'accounting', 'business_studies', 'geography', 'history',
    'arts', 'psychology', 'philosophy', 'statistics'
);

-- 5. Create comprehensive subjects table for better management
CREATE TABLE IF NOT EXISTS subjects (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    category subject_category NOT NULL,
    class_levels INTEGER[] NOT NULL,
    boards board_type[] NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Insert standard Pakistani curriculum subjects
INSERT INTO subjects (name, category, class_levels, boards, description) VALUES
-- Mathematics
('Mathematics', 'mathematics', ARRAY[5,6,7,8,9,10,11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Core mathematics including algebra, geometry, trigonometry'),
('Additional Mathematics', 'mathematics', ARRAY[9,10,11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Advanced mathematics topics'),

-- Sciences
('Physics', 'physics', ARRAY[9,10,11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Fundamental physics concepts and applications'),
('Chemistry', 'chemistry', ARRAY[9,10,11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Chemical principles and laboratory work'),
('Biology', 'biology', ARRAY[9,10,11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Life sciences and biological processes'),
('Computer Science', 'computer_science', ARRAY[9,10,11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Programming, algorithms, and computer systems'),

-- Languages
('English', 'english', ARRAY[5,6,7,8,9,10,11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'English language and literature'),
('Urdu', 'urdu', ARRAY[5,6,7,8,9,10,11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Urdu language and literature'),

-- Social Studies
('Pakistan Studies', 'pakistan_studies', ARRAY[9,10,11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'History and geography of Pakistan'),
('Islamiat', 'islamiat', ARRAY[5,6,7,8,9,10,11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Islamic studies and teachings'),
('Social Studies', 'social_studies', ARRAY[5,6,7,8], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Basic social sciences'),

-- Commerce & Business
('Economics', 'economics', ARRAY[11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Economic principles and theories'),
('Accounting', 'accounting', ARRAY[11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Financial accounting and bookkeeping'),
('Business Studies', 'business_studies', ARRAY[11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Business management and entrepreneurship'),

-- Others
('Geography', 'geography', ARRAY[9,10,11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Physical and human geography'),
('History', 'history', ARRAY[9,10,11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'World and regional history'),
('Statistics', 'statistics', ARRAY[11,12], ARRAY['punjab','sindh','kpk','federal','balochistan']::board_type[], 'Statistical analysis and probability')

ON CONFLICT (name) DO NOTHING;

-- 7. Create teacher_subjects junction table for many-to-many relationship
CREATE TABLE IF NOT EXISTS teacher_subjects (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    teacher_id UUID REFERENCES teachers(id) ON DELETE CASCADE NOT NULL,
    subject_id UUID REFERENCES subjects(id) ON DELETE CASCADE NOT NULL,
    class_levels INTEGER[] NOT NULL,
    proficiency_level TEXT CHECK (proficiency_level IN ('beginner', 'intermediate', 'advanced', 'expert')) DEFAULT 'intermediate',
    years_experience INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(teacher_id, subject_id)
);

-- 8. Create student_interests junction table
CREATE TABLE IF NOT EXISTS student_interests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    student_id UUID REFERENCES students(id) ON DELETE CASCADE NOT NULL,
    subject_id UUID REFERENCES subjects(id) ON DELETE CASCADE NOT NULL,
    interest_level TEXT CHECK (interest_level IN ('low', 'medium', 'high')) DEFAULT 'medium',
    current_grade TEXT CHECK (current_grade IN ('A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'F', 'not_studied')),
    needs_help BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, subject_id)
);

-- 9. Add indexes for new tables
CREATE INDEX idx_subjects_category ON subjects(category);
CREATE INDEX idx_subjects_class_levels ON subjects USING GIN(class_levels);
CREATE INDEX idx_teacher_subjects_teacher ON teacher_subjects(teacher_id);
CREATE INDEX idx_teacher_subjects_subject ON teacher_subjects(subject_id);
CREATE INDEX idx_student_interests_student ON student_interests(student_id);
CREATE INDEX idx_student_interests_subject ON student_interests(subject_id);

-- 10. Enable RLS for new tables
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_interests ENABLE ROW LEVEL SECURITY;

-- 11. Create RLS policies for new tables
CREATE POLICY "Subjects are viewable by everyone" ON subjects FOR SELECT USING (true);
CREATE POLICY "Teacher subjects are viewable by everyone" ON teacher_subjects FOR SELECT USING (true);
CREATE POLICY "Teachers can manage own subject mappings" ON teacher_subjects 
    FOR ALL USING (auth.uid() IN (SELECT profile_id FROM teachers WHERE id = teacher_id));

CREATE POLICY "Student interests are viewable by everyone" ON student_interests FOR SELECT USING (true);
CREATE POLICY "Students can manage own interests" ON student_interests 
    FOR ALL USING (auth.uid() IN (SELECT profile_id FROM students WHERE id = student_id));

-- 12. Add triggers for updated_at on new tables
CREATE TRIGGER update_teacher_subjects_updated_at BEFORE UPDATE ON teacher_subjects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_student_interests_updated_at BEFORE UPDATE ON student_interests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
