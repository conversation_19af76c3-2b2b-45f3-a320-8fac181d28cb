# EduBridge Setup Guide

## 🔑 Required API Keys

To get EduBridge fully functional, you only need **2 API keys**:

### 1. Supabase (Database + Authentication)
- **Website:** https://supabase.com
- **Purpose:** User authentication, database, file storage
- **Cost:** Free tier (up to 50,000 monthly active users)

**Steps:**
1. Create a new project on Supabase
2. Go to Settings → API
3. Copy these values to your `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
```

### 2. AI Models (Choose One - Groq Recommended)

#### **Option A: Groq (Recommended - Fast & Free)**
- **Website:** https://console.groq.com
- **Purpose:** AI tutoring with Llama 3.1 70B
- **Cost:** FREE (30,000 tokens/day)
- **Speed:** 500+ tokens/second (extremely fast)

**Steps:**
1. Create free account on Groq
2. Generate API key
3. Add to `.env.local`:

```env
GROQ_API_KEY=gsk-your-groq-key-here
```

#### **Option B: Local Models (100% Free)**
- **Setup:** Install Ollama + Llama 3.1 8B
- **Cost:** FREE forever (no API costs)
- **See:** `AI_SETUP_GUIDE.md` for detailed instructions

#### **Option C: OpenAI (Expensive)**
- **Cost:** ~$0.03 per 1K tokens (GPT-4)
- **Only use if you specifically need GPT-4**

## 📁 Complete .env.local File

Create a `.env.local` file in your project root with:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# AI Configuration (Choose one)
GROQ_API_KEY=gsk-your-groq-key-here
# OR
# OPENAI_API_KEY=sk-your-openai-key-here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 💳 Payment System Status

**Current Status:** MOCK PAYMENTS (Automatic Success)
- ✅ Click "Subscribe" → Automatically subscribed
- ✅ Click "Enroll" → Automatically enrolled
- ✅ Database gets updated with subscription/purchase records
- ✅ Users get immediate access to content
- ✅ No real money is charged

**Mock Payment Features:**
- Simulates JazzCash, EasyPaisa, Credit Cards
- Shows success toast notifications
- Generates realistic transaction IDs
- Updates database automatically
- 2-second processing delay for realism

**Database Updates:**
- Creates subscription records in `subscriptions` table
- Creates purchase records in `course_purchases` table
- Updates teacher/course statistics
- Sets proper expiration dates for subscriptions

## 🚀 Getting Started

1. **Set up Supabase database:**
   - Go to: https://cosmaeoxoprxbacgacwr.supabase.co
   - Open SQL Editor
   - Copy the SQL from `database/schema.sql`
   - Run it in your Supabase SQL editor

2. **Get your Supabase Service Role Key:**
   - Go to: https://cosmaeoxoprxbacgacwr.supabase.co/project/settings/api
   - Copy the "service_role" key
   - Add it to your `.env.local`

3. **Set up Local AI (Ollama):**
   - Follow instructions in `LOCAL_AI_SETUP.md`
   - Install Ollama: `brew install ollama`
   - Download model: `ollama pull llama3.1:8b`
   - Start server: `ollama serve`

4. **Run the development server:**
   ```bash
   npm run dev
   ```

5. **Test the full system:**
   - Open http://localhost:3000
   - Sign up as student/teacher
   - Test AI tutoring at `/student/ai-tutor`
   - Test subscriptions at `/student/teachers`

## 📋 Current Features

✅ **Working:**
- Beautiful responsive homepage
- Navigation with role-based menus
- UI components and animations
- Mock payment system
- Project structure ready for scaling

🔄 **Next to implement:**
- Supabase database setup
- User authentication
- AI tutoring integration
- Student/Teacher dashboards
- Course management

## 🎯 Priority Implementation Order

1. **Supabase Setup** - Database schema and authentication
2. **User Registration** - Student/Teacher signup flows
3. **AI Integration** - OpenAI tutoring features
4. **Dashboards** - Student and Teacher portals
5. **Content Management** - Course creation and enrollment
6. **Payment Integration** - Real payment gateways (later)

## 📞 Support

If you need help getting the API keys or setting up the project, let me know!

---

**EduBridge** - Transforming Education in Pakistan 🇵🇰
