'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/auth-provider'

export function AuthRedirectHandler() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // Only run this effect in the browser
    if (typeof window === 'undefined') return

    const handleRedirect = async () => {
      // Check if user landed on /# after email confirmation
      if (window.location.hash === '#' && window.location.pathname === '/') {
        if (!loading && user) {
          // User is authenticated and on /#, redirect to appropriate dashboard
          switch (user.role) {
            case 'admin':
              router.replace('/admin/dashboard')
              break
            case 'teacher':
              router.replace('/teacher/dashboard')
              break
            case 'student':
              router.replace('/student/dashboard')
              break
            default:
              // If no role, stay on homepage but remove the hash
              router.replace('/')
              break
          }
        }
      }
    }

    // Run immediately
    handleRedirect()

    // Also listen for hash changes
    const handleHashChange = () => {
      handleRedirect()
    }

    window.addEventListener('hashchange', handleHashChange)
    
    return () => {
      window.removeEventListener('hashchange', handleHashChange)
    }
  }, [user, loading, router])

  return null // This component doesn't render anything
}
