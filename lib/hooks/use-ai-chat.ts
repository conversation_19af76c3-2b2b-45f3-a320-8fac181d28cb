'use client'

import { useState, useCallback } from 'react'
import { toast } from 'sonner'

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
  timestamp?: string
}

export interface ChatContext {
  subject?: string
  classLevel?: number
  board?: string
  studentLevel?: 'beginner' | 'intermediate' | 'advanced'
}

export interface AIStatus {
  running: boolean
  modelAvailable: boolean
  model?: string
  baseUrl?: string
  error?: string
}

export function useAIChat() {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [aiStatus, setAIStatus] = useState<AIStatus | null>(null)

  // Check AI status
  const checkAIStatus = useCallback(async (): Promise<AIStatus> => {
    try {
      const response = await fetch('/api/ai/status')
      const data = await response.json()
      
      if (data.success) {
        setAIStatus(data.status)
        return data.status
      } else {
        const status = { running: false, modelAvailable: false, error: data.error }
        setAIStatus(status)
        return status
      }
    } catch (error) {
      const status = { 
        running: false, 
        modelAvailable: false, 
        error: 'Failed to check AI status' 
      }
      setAIStatus(status)
      return status
    }
  }, [])

  // Send message to AI
  const sendMessage = useCallback(async (
    message: string,
    context?: ChatContext
  ): Promise<string | null> => {
    setIsLoading(true)
    
    try {
      // Add user message to conversation
      const userMessage: ChatMessage = {
        role: 'user',
        content: message,
        timestamp: new Date().toISOString()
      }
      
      setMessages(prev => [...prev, userMessage])

      // Send to AI
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          context,
          conversationHistory: messages
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get AI response')
      }

      // Add AI response to conversation
      const aiMessage: ChatMessage = {
        role: 'assistant',
        content: data.response,
        timestamp: data.timestamp
      }
      
      setMessages(prev => [...prev, aiMessage])
      
      return data.response

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get AI response'
      
      // Show error toast
      toast.error('AI Error', {
        description: errorMessage,
        duration: 5000,
      })

      // Add error message to conversation
      const errorMsg: ChatMessage = {
        role: 'assistant',
        content: `I'm sorry, I encountered an error: ${errorMessage}. Please make sure Ollama is running with 'ollama serve' and the model is downloaded.`,
        timestamp: new Date().toISOString()
      }
      
      setMessages(prev => [...prev, errorMsg])
      
      return null
    } finally {
      setIsLoading(false)
    }
  }, [messages])

  // Clear conversation
  const clearMessages = useCallback(() => {
    setMessages([])
  }, [])

  // Add system message
  const addSystemMessage = useCallback((content: string) => {
    const systemMessage: ChatMessage = {
      role: 'system',
      content,
      timestamp: new Date().toISOString()
    }
    setMessages(prev => [systemMessage, ...prev])
  }, [])

  // Get conversation summary
  const getConversationSummary = useCallback(() => {
    const userMessages = messages.filter(m => m.role === 'user').length
    const aiMessages = messages.filter(m => m.role === 'assistant').length
    
    return {
      totalMessages: messages.length,
      userMessages,
      aiMessages,
      lastMessage: messages[messages.length - 1]
    }
  }, [messages])

  return {
    messages,
    isLoading,
    aiStatus,
    sendMessage,
    clearMessages,
    addSystemMessage,
    checkAIStatus,
    getConversationSummary
  }
}

// Hook for AI tutoring with Pakistani curriculum context
export function useAITutor(initialContext?: ChatContext) {
  const aiChat = useAIChat()
  
  const askTutor = useCallback(async (question: string, context?: ChatContext) => {
    const tutorContext = {
      ...initialContext,
      ...context
    }
    
    // Add context to the question if provided
    let contextualQuestion = question
    if (tutorContext.subject || tutorContext.classLevel || tutorContext.board) {
      const contextParts = []
      if (tutorContext.classLevel) contextParts.push(`Class ${tutorContext.classLevel}`)
      if (tutorContext.subject) contextParts.push(tutorContext.subject)
      if (tutorContext.board) contextParts.push(`${tutorContext.board} board`)
      
      contextualQuestion = `[Context: ${contextParts.join(', ')}] ${question}`
    }
    
    return aiChat.sendMessage(contextualQuestion, tutorContext)
  }, [aiChat, initialContext])

  const generateQuiz = useCallback(async (
    subject: string,
    classLevel: number,
    board: string,
    numQuestions: number = 5,
    difficulty: 'easy' | 'medium' | 'hard' = 'medium'
  ) => {
    const quizPrompt = `Generate a ${difficulty} level quiz for ${subject} for Class ${classLevel} students following the ${board} board curriculum in Pakistan. Create exactly ${numQuestions} multiple choice questions with 4 options each. Include the correct answer and explanation for each question. Format as JSON.`
    
    return aiChat.sendMessage(quizPrompt, {
      subject,
      classLevel,
      board
    })
  }, [aiChat])

  const explainConcept = useCallback(async (
    concept: string,
    subject: string,
    classLevel: number,
    board: string
  ) => {
    const explanationPrompt = `Explain the concept of "${concept}" for a Class ${classLevel} ${subject} student following the ${board} board curriculum in Pakistan. Use simple language, provide examples, and break it down step by step.`
    
    return aiChat.sendMessage(explanationPrompt, {
      subject,
      classLevel,
      board
    })
  }, [aiChat])

  return {
    ...aiChat,
    askTutor,
    generateQuiz,
    explainConcept
  }
}
