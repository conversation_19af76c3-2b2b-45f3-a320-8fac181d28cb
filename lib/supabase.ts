import { createClient } from '@supabase/supabase-js'
import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import type { Database } from './database.types'

// New Supabase configuration
const supabaseUrl = 'https://yscalbrckcblxdmmiohy.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlzY2FsYnJja2NibHhkbW1pb2h5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1NjM4NjQsImV4cCI6MjA2NTEzOTg2NH0.5XCoqiYAfKf6BxrT_sXLclXa3QckeW_Pq3HaAtKccDI'

// Client-side Supabase client with simplified auth config
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    flowType: 'pkce',
    autoRefreshToken: true,
    detectSessionInUrl: true,
    persistSession: true,
  }
})

// Client component Supabase client
export const createClientSupabase = () => createClientComponentClient<Database>()

// Server component Supabase client (only use in server components)
export const createServerSupabase = () => {
  const { cookies } = require('next/headers')
  return createServerComponentClient<Database>({ cookies })
}

// Admin client for server-side operations (will need service role key)
export const supabaseAdmin = createClient<Database>(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY || supabaseAnonKey, // Fallback to anon key for now
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)
