# EduBridge Authentication Setup Guide

## Overview

This guide explains the new, simplified authentication system for EduBridge. The authentication flow has been completely rebuilt with clean, easy-to-understand logic.

## Authentication Flow

### 1. Sign Up Process
1. User fills out signup form (email, password, name, role)
2. Supa<PERSON> creates auth user with metadata
3. User receives email confirmation
4. User clicks confirmation link
5. User is redirected to profile setup
6. Profile is created in database
7. User is redirected to dashboard

### 2. Sign In Process
1. User enters email and password
2. System checks if profile exists
3. If no profile: redirect to profile setup
4. If profile exists: redirect to appropriate dashboard

## Database Schema

The new schema is simplified and focused:

- **profiles**: Basic user information (extends auth.users)
- **students**: Student-specific data
- **teachers**: Teacher-specific data
- **courses**: Course information
- **subscriptions**: Teacher subscriptions
- **course_purchases**: Course purchases

## Setup Instructions

### 1. Database Setup

Run the SQL script in your Supabase dashboard:

```sql
-- Copy and paste the contents of database/new-schema.sql
```

### 2. Environment Variables

Update your `.env.local` file:

```env
NEXT_PUBLIC_SUPABASE_URL=https://yscalbrckcblxdmmiohy.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlzY2FsYnJja2NibHhkbW1pb2h5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1NjM4NjQsImV4cCI6MjA2NTEzOTg2NH0.5XCoqiYAfKf6BxrT_sXLclXa3QckeW_Pq3HaAtKccDI
```

### 3. Test the Setup

Run the test script:

```bash
node scripts/test-auth-flow.js
```

### 4. Start Development Server

```bash
npm run dev
```

## Key Features

### ✅ Simplified Logic
- No complex profile creation during signup
- Clean separation of auth and profile data
- Easy-to-follow redirect logic

### ✅ Proper Email Confirmation
- Users must confirm email before profile setup
- No profile data created until confirmation
- Clear user feedback throughout process

### ✅ Role-Based Routing
- Automatic redirects based on user role
- Protected routes with middleware
- Proper profile setup flow

### ✅ Error Handling
- User-friendly error messages
- Graceful fallbacks
- Clear debugging information

## File Structure

```
app/auth/
├── signup/page.tsx          # Simplified signup form
├── signin/page.tsx          # Simplified signin form
├── confirm/page.tsx         # Email confirmation handler
└── check-email/page.tsx     # Email confirmation waiting page

app/student/
└── setup-profile/page.tsx   # Student profile setup

app/teacher/
└── setup-profile/page.tsx   # Teacher profile setup

lib/
└── supabase.ts              # Updated Supabase configuration

components/providers/
└── auth-provider.tsx        # Updated auth context

database/
└── new-schema.sql           # Complete database schema
```

## Troubleshooting

### Common Issues

1. **Email confirmation not working**
   - Check Supabase email settings
   - Verify redirect URL configuration
   - Check spam folder

2. **Profile creation fails**
   - Verify database schema is applied
   - Check RLS policies
   - Review console errors

3. **Redirect loops**
   - Clear browser cache
   - Check middleware configuration
   - Verify user role in metadata

### Debug Mode

Enable debug logging by adding to your component:

```javascript
console.log('User:', user)
console.log('Session:', session)
console.log('Profile:', profile)
```

## Testing Checklist

- [ ] User can sign up with email/password
- [ ] Email confirmation link works
- [ ] Profile setup completes successfully
- [ ] User can sign in after setup
- [ ] Proper redirects based on role
- [ ] Protected routes work correctly
- [ ] Sign out works properly

## Support

If you encounter issues:

1. Check the console for error messages
2. Verify your Supabase configuration
3. Run the test script to check connectivity
4. Review the authentication flow step by step

The new authentication system is designed to be simple, reliable, and easy to debug. Follow this guide step by step for a smooth setup experience.
