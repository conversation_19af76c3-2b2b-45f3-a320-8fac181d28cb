import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import type { Database } from '@/lib/database.types'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient<Database>({ req, res })

  const {
    data: { session },
  } = await supabase.auth.getSession()

  const { pathname } = req.nextUrl

  // Public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/auth/signin',
    '/auth/signup',
    '/auth/forgot-password',
    '/auth/reset-password',
    '/auth/confirm',
    '/auth/check-email',
    '/auth/complete-profile',
    '/student/setup-profile',
    '/teacher/setup-profile',
    '/about',
    '/contact',
    '/privacy',
    '/terms',
  ]

  // API routes that don't require authentication
  const publicApiRoutes = [
    '/api/auth',
    '/api/webhooks',
  ]

  // Check if the current path is public
  const isPublicRoute = publicRoutes.some(route => pathname === route || pathname.startsWith(route))
  const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route))

  // If it's a public route or public API route, allow access
  if (isPublicRoute || isPublicApiRoute) {
    return res
  }

  // If user is not authenticated and trying to access protected route
  if (!session) {
    const redirectUrl = new URL('/auth/signin', req.url)
    redirectUrl.searchParams.set('redirectTo', pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // Get user profile to check role
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', session.user.id)
    .single()

  if (profileError || !profile) {
    // If profile doesn't exist, redirect to appropriate setup page based on user metadata
    const userRole = session.user.user_metadata?.role || 'student'

    if (userRole === 'teacher') {
      return NextResponse.redirect(new URL('/teacher/setup-profile', req.url))
    } else {
      return NextResponse.redirect(new URL('/student/setup-profile', req.url))
    }
  }

  // Role-based route protection
  const userRole = profile.role

  // Admin routes
  if (pathname.startsWith('/admin')) {
    if (userRole !== 'admin') {
      return NextResponse.redirect(new URL('/unauthorized', req.url))
    }
  }

  // Teacher routes
  if (pathname.startsWith('/teacher')) {
    if (userRole !== 'teacher' && userRole !== 'admin') {
      return NextResponse.redirect(new URL('/unauthorized', req.url))
    }
  }

  // Student routes
  if (pathname.startsWith('/student')) {
    if (userRole !== 'student' && userRole !== 'admin') {
      return NextResponse.redirect(new URL('/unauthorized', req.url))
    }
  }

  // Dashboard redirect based on role
  if (pathname === '/dashboard') {
    switch (userRole) {
      case 'admin':
        return NextResponse.redirect(new URL('/admin/dashboard', req.url))
      case 'teacher':
        return NextResponse.redirect(new URL('/teacher/dashboard', req.url))
      case 'student':
        return NextResponse.redirect(new URL('/student/dashboard', req.url))
      default:
        return NextResponse.redirect(new URL('/auth/complete-profile', req.url))
    }
  }

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
