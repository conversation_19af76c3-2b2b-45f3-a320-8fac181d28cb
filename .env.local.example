# ===== REQUIRED API KEYS =====
# These are the only keys you need to get started

# Supabase Configuration (Database + Authentication)
NEXT_PUBLIC_SUPABASE_URL=https://yscalbrckcblxdmmiohy.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlzY2FsYnJja2NibHhkbW1pb2h5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1NjM4NjQsImV4cCI6MjA2NTEzOTg2NH0.5XCoqiYAfKf6BxrT_sXLclXa3QckeW_Pq3HaAtKccDI
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# ===== AI CONFIGURATION (Choose one or more) =====

# Groq - RECOMMENDED (Fast, Free, High Quality)
GROQ_API_KEY=gsk_your-groq-key-here

# OpenAI - EXPENSIVE (Only if you want GPT-4)
# OPENAI_API_KEY=your_openai_api_key

# Hugging Face - FREE Alternative
# HUGGINGFACE_API_KEY=hf_your-huggingface-key

# Local Ollama - FREE, No limits (if running locally)
# OLLAMA_BASE_URL=http://localhost:11434

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# ===== PAYMENT INTEGRATION (DISABLED FOR NOW) =====
# Payments are currently mocked - these will be used later
# STRIPE_SECRET_KEY=your_stripe_secret_key
# NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
# JAZZCASH_MERCHANT_ID=your_jazzcash_merchant_id
# JAZZCASH_PASSWORD=your_jazzcash_password
# EASYPAISA_STORE_ID=your_easypaisa_store_id
